module.exports = {
  // Use semicolons at the end of each statement
  semi: true,
  // Use trailing commas when there are multiple items in an array or object
  trailingComma: 'all',
  // Use single quotes for strings
  singleQuote: true,
  // Limit the line length to 160 characters
  printWidth: 160,
  // Use 2 spaces for indentation
  tabWidth: 2,
  // Use the platform's default end-of-line character (LF on Unix-based systems, CRLF on Windows)
  endOfLine: 'auto',
  // Use single quotes for JSX attributes
  jsxSingleQuote: true,
  // Place the closing bracket of a multi-line JSX element on a new line
  jsxBracketSameLine: false,
  // Always include parentheses for arrow function parameters
  arrowParens: 'always',
  // Override some settings for Markdown files
  overrides: [
    {
      files: '*.md',
      options: {
        // Wrap prose at the configured line length
        proseWrap: 'always',
      },
    },
  ],
};
