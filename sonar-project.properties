# must be unique in a sonarQube instance
sonar.projectKey=WVDB-infrastructure

# optional properties
sonar.projectName=WVDB-infrastructure

# language
sonar.language=typescript

# source code
sonar.sources=bin,lib

# TypeScript-Konfigurationsdatei (optional, falls Sie eine spezielle tsconfig-<PERSON><PERSON> verwenden möchten)
sonar.typescript.tsconfigPath=tsconfig.json

# encoding
sonar.sourceEncoding=UTF-8

# sonar host
sonar.host.url=https://skyway.porsche.com/sonarqube

# exclusion: all base classes, actions, dao, dialoge, editmodel, editor, nodemodel, wizard, uc
sonar.exclusions=node_modules/**, **/*.test.ts, **/*.test.tsx

# repository
sonar.links.scm=https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure.git
