#!/bin/bash

# Prompt for user input and read the variables
read -p "stage: " stage
read -p "ppnClientSecret: " ppnClientSecret
read -p "ppnU2AClientSecret: " ppnU2AClientSecret
read -p "ppnMasterClientSecret: " ppnMasterClientSecret

# Bedingte Zuweisung von Werten basierend auf dem Wert von "stage"
if [ "$stage" == "dev" ]; then
  profile="779846826479_Developer"
elif [ "$stage" == "prod" ]; then
  profile="UNDEFINED"
fi

echo "stage = $stage"
echo "profile = $profile"


cdkCommand="cdk deploy --all --context stage=$stage --context ppnClientSecret=$ppnClientSecret --context ppnMasterClientSecret=$ppnMasterClientSecret --context ppnU2AClientSecret=$ppnU2AClientSecret --require-approval never --profile $profile"


echo $cdkCommand
eval $cdkCommand 

ppnClientSecretName="$stage-wvdb-ppn-client-secret"
ppnMasterClientSecretName="$stage-wvdb-ppn-master-client-secret"
ppnU2AClientSecretName="$stage-wvdb-ppn-u2a-client-secret"
type="SecureString"
keyId="alias/$stage-wvdb-ssm-cmk"
updatePPNClientSecretNameCmd="aws ssm put-parameter --name $ppnClientSecretName --value $ppnClientSecret --type $type --key-id $keyId --overwrite --profile $profile --region eu-west-1"
echo $updatePPNClientSecretNameCmd
eval $updatePPNClientSecretNameCmd
updatePPNMasterClientSecretNameCmd="aws ssm put-parameter --name $ppnMasterClientSecretName --value $ppnMasterClientSecret --type $type --key-id $keyId --overwrite --profile $profile --region eu-west-1"
echo $updatePPNMasterClientSecretNameCmd
eval $updatePPNMasterClientSecretNameCmd
updatePPNU2AClientSecretNameCmd="aws ssm put-parameter --name $ppnU2AClientSecretName --value $ppnU2AClientSecret --type $type --key-id $keyId --overwrite --profile $profile --region eu-west-1"
echo $updatePPNU2AClientSecretNameCmd
eval $updatePPNU2AClientSecretNameCmd