{"name": "wvdb-infrastructure", "version": "0.1.3", "bin": {"wvdb-infrastructure": "bin/wvdb-infrastructure.js"}, "scripts": {"build": "tsc", "build_lambda": "cd resources/lambda && npm run build && cd -", "watch": "tsc -w", "test": "jest", "release": "standard-version", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.171.0", "husky": "^8.0.3", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"aws-cdk-lib": "2.171.0", "constructs": "^10.0.0", "standard-version": "^9.5.0"}, "publishConfig": {"@wvdb:registry": "https://cicd.skyway.porsche.com/api/v4/projects/34269/packages/npm/"}, "repository": {"type": "git", "url": "https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure.git"}, "standard-version": {"releaseCommitMessageFormat": "chore(release): {{currentTag}}"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}}