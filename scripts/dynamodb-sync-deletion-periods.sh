#!/bin/bash

TARGET_STAGE=$1
REGION="eu-west-1"
TABLE_NAME="${TARGET_STAGE}-wvdb-deletion-log"

PART1="resources/database/${TARGET_STAGE}/DeletionPeriods_Part1.json"
PART2="resources/database/${TARGET_STAGE}/DeletionPeriods_Part2.json"

if [ -z "$TARGET_STAGE" ]; then
    echo "Usage: $0 <target-stage>"
    exit 1
fi

echo "🔍 Prüfe, ob Daten bereits in $TABLE_NAME existieren..."

KEY=$(jq -c '.["dev-wvdb-deletion-log"][0].PutRequest.Item | {pk: .pk.S, sk: .sk.S}' "$PART1" 2>/dev/null)
if [ -z "$KEY" ]; then
  echo "Fehler beim Extrahieren des Keys aus der Datei $PART1."
  exit 1
fi

QUERY_KEY=$(echo "$KEY" | jq '{pk: {"S": .pk}, sk: {"S": .sk}}')

echo "QUERY_KEY: $QUERY_KEY"

EXISTS=$(aws dynamodb get-item --table-name "$TABLE_NAME" --key "$QUERY_KEY" --region "$REGION" | jq '.Item')

if [[ -z "$EXISTS" ]]; then
    echo "⚠️  Daten fehlen in $TABLE_NAME. Starte Upload..."
    aws dynamodb batch-write-item --request-items "file://$PART1" --region "$REGION"
    aws dynamodb batch-write-item --request-items "file://$PART2" --region "$REGION"
else
    echo "✅ Daten sind bereits in $TABLE_NAME. Kein Upload nötig."
fi

echo "✅ Synchronisierung abgeschlossen."
