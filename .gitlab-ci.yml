include:
  - project: 'porsche/builddeploy-templates'
    file: .gitlab-ci-template.yml
    ref: master

.aws_setup:
  image: node:latest
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://cicd.skyway.porsche.com
  before_script:
    - apt-get update
    - apt --assume-yes install awscli
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s"
      $(aws sts assume-role-with-web-identity
      --role-arn ${AWS_OIDC_IAM_ROLE_ARN}
      --role-session-name "Git<PERSON>ab<PERSON>unner-${CI_PROJECT_ID}-${CI_PIPELINE_ID}"
      --web-identity-token ${GITLAB_OIDC_TOKEN}
      --duration-seconds 3600
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]'
      --output text))
    - aws sts get-caller-identity
    - aws --version
    - npm install -g aws-cdk
    - npm install

variables:
  PROJECT_NAME: 'wvdb-infrastructure'
  JUNITFAILONCVSS: 4
  DEPENDENCY_CHECK_VERSION: '8.4.3'
  DEPENDENCY_CHECK_URL: 'https://github.com/jeremylong/DependencyCheck/releases/download/v$DEPENDENCY_CHECK_VERSION/dependency-check-$DEPENDENCY_CHECK_VERSION-release.zip'
  VERSION_REGEX: '/^chore\(release\):\s([0-9]+)\.([0-9]+)\.([0-9]+)/'
  ENVIRONMENT: ""


cache:
  paths:
    - node_modules/

stages:
  - build
  - validate
  - release
  - deploy
  - quality

Build:
  stage: build
  image: node:latest
  environment:
    name: $ENVIRONMENT
  script:
    - npm install
    - npm run build_lambda
    - npm run build
    - npm run cdk -- synth --context stage=$TARGET_STAGE --context ppnClientSecret=$PPN_CLIENT_SECRET --context ppnMasterClientSecret=$PPN_MASTER_CLIENT_SECRET --context ppnU2AClientSecret=$PPN_U2A_CLIENT_SECRET
    - mkdir -p cdk_templates
    - cp cdk.out/*.template.json cdk_templates/
  artifacts:
    paths:
      - cdk.out/
      - resources/lambda/
      - cdk_templates/
    expire_in: 1 week  
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ $VERSION_REGEX'
      when: never
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: $CI_COMMIT_BRANCH == "main" 
      variables:
        ENVIRONMENT: dev
    - if: $CI_COMMIT_BRANCH == "prod" 
      variables:
        ENVIRONMENT: prod
    - when: never                 

CFN Guard:
  stage: validate
  image: cr.cicd.skyway.porsche.com/ditp/shared-services/architektur-security/ditp-cfn-guardrules:latest
  needs:
    - job: Build
  allow_failure: true
  environment:
    name: $ENVIRONMENT  
  variables:
    RULE_PATH: /usr/src/cloudformation-guard/rules
    CF_TEMPLATE_PATH: cdk_templates/
  script:
    - /usr/src/cloudformation-guard/cfn-guard validate -r $RULE_PATH -d $CF_TEMPLATE_PATH --show-summary all --output-format json > gl-pag-security-check-report.json
  artifacts:
    paths:
      - 'gl-pag-security-check-report.json'
    when: always
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ $VERSION_REGEX'
      when: never
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: $CI_COMMIT_BRANCH == "main" 
      variables:
        ENVIRONMENT: dev
    - if: $CI_COMMIT_BRANCH == "prod" 
      variables:
        ENVIRONMENT: prod      
    - when: never     

Release:
  stage: release
  image: node:latest
  environment:
    name: $ENVIRONMENT  
  needs:
    - job: CFN Guard
    - job: Build
  before_script:
    - git config --global user.name "${GITLAB_USER_NAME}"
    - git config --global user.email "${GITLAB_USER_EMAIL}"
    - |
      {
        echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}"
      } | tee --append .npmrc    
  script:
    - npm install standard-version
    - npm run release
    - echo "git push -o ci-skip --follow-tags https://$USER_AND_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git HEAD:prod"
    - git push -o ci-skip --follow-tags https://$USER_AND_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git HEAD:prod
    - npm publish --registry "https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/"
    - git remote set-url origin https://$USER_AND_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git
    - ./scripts/update-version-of-master-branch.sh
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ $VERSION_REGEX'
      when: never
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: $CI_COMMIT_BRANCH == "prod" 
      variables:
        ENVIRONMENT: prod      
    - when: never   


Deploy:
  stage: deploy
  extends: .aws_setup
  environment:
    name: $ENVIRONMENT  
  needs:
    - job: CFN Guard
    - job: Build
    - job: Release
      optional: true
  script:
    - cdk deploy --all --context stage=$TARGET_STAGE --context region=$AWS_DEFAULT_REGION --context ppnClientSecret=$PPN_CLIENT_SECRET --context ppnMasterClientSecret=$PPN_MASTER_CLIENT_SECRET --context ppnU2AClientSecret=$PPN_U2A_CLIENT_SECRET --require-approval never
    - aws ssm put-parameter --name "$TARGET_STAGE-wvdb-ppn-client-secret" --value "$PPN_CLIENT_SECRET" --type "SecureString" --key-id "alias/$TARGET_STAGE-wvdb-ssm-cmk" --overwrite --region eu-west-1
    - aws ssm put-parameter --name "$TARGET_STAGE-wvdb-ppn-master-client-secret" --value "$PPN_MASTER_CLIENT_SECRET" --type "SecureString" --key-id "alias/$TARGET_STAGE-wvdb-ssm-cmk" --overwrite --region eu-west-1
    - aws ssm put-parameter --name "$TARGET_STAGE-wvdb-ppn-u2a-client-secret" --value "$PPN_U2A_CLIENT_SECRET" --type "SecureString" --key-id "alias/$TARGET_STAGE-wvdb-ssm-cmk" --overwrite --region eu-west-1
    - apt-get update && apt-get install -y jq
    - ./scripts/dynamodb-sync-deletion-periods.sh "$TARGET_STAGE"
    #- aws dynamodb batch-write-item --request-items file://resources/database/$TARGET_STAGE/DeletionPeriods_Part1.json --region eu-west-1 
    #- aws dynamodb batch-write-item --request-items file://resources/database/$TARGET_STAGE/DeletionPeriods_Part2.json --region eu-west-1 


  rules:
    - if: '$CI_COMMIT_MESSAGE =~ $VERSION_REGEX'
      when: never
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: $CI_COMMIT_BRANCH == "main" 
      variables:
        ENVIRONMENT: dev
    - if: $CI_COMMIT_BRANCH == "prod" 
      variables:
        ENVIRONMENT: prod      
    - when: never   

Pages:
  stage: quality
  environment:
    name: $ENVIRONMENT   
  needs:
    - job: Dependency Check
  pages: true  
  script:
    - mkdir .public
    - mv public/* .public
    - mv nvd/dependency-check-report.html .public/dependencies.html
    - mv -v .public/* public/
  artifacts:
    paths:
      - public
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ $VERSION_REGEX'
      when: never
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: $CI_COMMIT_BRANCH == "main" 
      variables:
        ENVIRONMENT: dev  
    - when: never   

Dependency Check:
  image: node:latest
  stage: quality
  before_script:
    - apt-get update -qy
    - apt-get install -y default-jre
    - JAVA_HOME="/usr/lib/jvm/java-17-openjdk-amd64/"
    - export JAVA_HOME
  script:
    - npm --version
    - echo "curl -LO $DEPENDENCY_CHECK_URL"
    - curl -LO $DEPENDENCY_CHECK_URL
    - unzip dependency-check-*
    - export PATH=dependency-check/bin:$PATH
    - mkdir -p nvd
    - rm -rf node_modules/
    - npm install
    - dependency-check.sh --project $PROJECT_NAME -s ./package.json -s ./package-lock.json -f HTML --nodeAuditSkipDevDependencies --nodePackageSkipDevDependencies --disableNuspec --disableAssembly --disablePnpmAudit -d nvd --junitFailOnCVSS $JUNITFAILONCVSS --out ./nvd
  artifacts:
    paths:
      - 'nvd/dependency-check-report.html'
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ $VERSION_REGEX'
      when: never
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: $CI_COMMIT_BRANCH == "main" 
      variables:
        ENVIRONMENT: dev  
    - when: never   

Sonarqube Scan:
  extends: .sonarqube
  environment:
    name: $ENVIRONMENT   
  script:
    - cd $CI_PROJECT_DIR
    - export APP_VERSION=$(cat package.json | grep version | head -n 1)
    - echo $APP_VERSION > $CI_PROJECT_DIR/version.txt
    - echo -e "\nsonar.projectVersion=$(cat $CI_PROJECT_DIR/version.txt)\n" >> sonar-project.properties
    - sonar-scanner -Dsonar.login=$SONAR_TOKEN -Dsonar.branch.name=main -Dsonar.qualitygate.wait=true
  stage: quality
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ $VERSION_REGEX'
      when: never
    - if: '$CI_COMMIT_TAG'
      when: never
    - if: $CI_COMMIT_BRANCH == "main" 
      variables:
        ENVIRONMENT: dev   
    - when: never   
