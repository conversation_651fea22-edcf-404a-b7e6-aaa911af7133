# Session Token Refresh Problem - Projektplan

## Problem Analyse

Das Problem ist, dass Sessions nach ca. 15 Minuten ablaufen und Backend-Calls dann fehlschlagen. Der gewünschte Ansatz ist, dass wenn ein ungültiges Session-Token ankommt, es automatisch verlängert werden soll.

### Aktuelle Architektur

1. **Authentication Flow:**
   - Login über PPN (Porsche Partner Network)
   - `codeExchangeLambda.ts` tauscht Authorization Code gegen Access Token
   - Token wird als HttpOnly Cookie gesetzt mit Max-Age basierend auf Token-Expiration (Standard: 15 Minuten)
   - Cookies: `Authorization`, `UserAttributes`, `SessionExp`

2. **Authorization:**
   - `authorizerLambda.ts` validiert JWT Token bei jedem API-Request
   - Bei ungültigem Token: `throw new Error('Unauthorized')` → 401 Response
   - API Gateway redirected bei 401 automatisch zum Login

3. **Frontend:**
   - Statische HTML-Seite ohne JavaScript Session-Management
   - Keine automatische Token-Refresh-Logik

### Identifizierte Probleme

1. **Kein Token-Refresh-Mechanismus:** Wenn Token abläuft, muss User sich komplett neu anmelden
2. **Fehlende Frontend-Session-Überwachung:** Keine Überwachung der Session-Expiration
3. **Authorizer wirft sofort Fehler:** Keine Möglichkeit für automatische Token-Verlängerung

## Lösungsansätze

### Option 1: Token-Refresh im Authorizer (Empfohlen)
- **Vorteil:** Transparent für Frontend, funktioniert mit bestehender Architektur
- **Implementierung:** Authorizer versucht Token-Refresh bevor er Deny Policy zurückgibt

### Option 2: Frontend-basierte Token-Refresh
- **Vorteil:** Mehr Kontrolle über Session-Management
- **Nachteil:** Erfordert JavaScript-Implementierung im Frontend

### Option 3: Längere Token-Laufzeit
- **Vorteil:** Einfachste Lösung
- **Nachteil:** Sicherheitsrisiko, löst Problem nicht grundsätzlich

## Empfohlene Lösung: Option 1 - Token-Refresh im Authorizer

### Todo Liste

- [ ] 1. **Analyse der Token-Refresh-Möglichkeiten**
  - [ ] Prüfen ob PPN Token-Refresh unterstützt
  - [ ] Dokumentation der verfügbaren Token-Endpoints

- [ ] 2. **Implementierung Token-Refresh-Funktion**
  - [ ] Neue Funktion `refreshToken()` in authorizerLambda.ts
  - [ ] Integration mit PPN Token-Endpoint
  - [ ] Fehlerbehandlung für Refresh-Failures

- [ ] 3. **Modifikation des Authorizers**
  - [ ] Bei Token-Validation-Fehler: Versuch Token-Refresh
  - [ ] Bei erfolgreichem Refresh: Neue Cookies setzen und Allow Policy zurückgeben
  - [ ] Bei Refresh-Fehler: Deny Policy wie bisher

- [ ] 4. **Cookie-Management erweitern**
  - [ ] Neue Cookie-Werte in Authorizer-Response setzen
  - [ ] SessionExp Cookie aktualisieren

- [ ] 5. **Testing und Validation**
  - [ ] Unit Tests für Token-Refresh-Logik
  - [ ] Integration Tests mit abgelaufenen Tokens
  - [ ] End-to-End Tests mit Frontend

- [ ] 6. **Monitoring und Logging**
  - [ ] Logging für Token-Refresh-Versuche
  - [ ] Metriken für erfolgreiche/fehlgeschlagene Refreshs

### Technische Details

**Neue Environment Variables für Authorizer:**
- `TOKEN_URL`: PPN Token-Endpoint für Refresh
- `CLIENT_SECRET_PARAMETER`: Für Token-Refresh-Requests

**Neue Funktionen:**
- `refreshToken(expiredToken: string): Promise<string | null>`
- `updateCookiesInResponse(response: APIGatewayAuthorizerResult, newToken: string): APIGatewayAuthorizerResult`

**Modifikation in validateToken():**
- Bei JWT-Verification-Fehler: Prüfen ob Token nur abgelaufen ist
- Falls ja: Token-Refresh versuchen
- Bei erfolgreichem Refresh: Neue Token-Validation

### Fallback-Strategie

Falls Token-Refresh nicht möglich:
- Frontend-JavaScript für Session-Überwachung implementieren
- Automatische Weiterleitung zum Login vor Token-Ablauf
- Warnung an User vor Session-Ablauf

## Review Sektion

*Wird nach Implementierung ausgefüllt*
