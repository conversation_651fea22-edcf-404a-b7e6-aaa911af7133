# Session Token Refresh Problem - Projektplan

## Problem Analyse

Das Problem ist, dass Sessions nach ca. 15 Minuten ablaufen und Backend-Calls dann fehlschlagen. Der gewünschte Ansatz ist, dass wenn ein ungültiges
Session-Token ankommt, es automatisch verlängert werden soll.

### Aktuelle Architektur

1. **Authentication Flow:**

   - Login über PPN (Porsche Partner Network)
   - `codeExchangeLambda.ts` tauscht Authorization Code gegen Access Token
   - Token wird als HttpOnly Cookie gesetzt mit Max-Age basierend auf Token-Expiration (Standard: 15 Minuten)
   - Cookies: `Authorization`, `UserAttributes`, `SessionExp`

2. **Authorization:**

   - `authorizerLambda.ts` validiert JWT Token bei jedem API-Request
   - Bei ungültigem Token: `throw new Error('Unauthorized')` → 401 Response
   - API Gateway redirected bei 401 automatisch zum Login

3. **Frontend:**
   - Statische HTML-Seite ohne JavaScript Session-Management
   - Keine automatische Token-Refresh-Logik

### Identifizierte Probleme

1. **Kein Token-Refresh-Mechanismus:** Wenn Token abläuft, muss User sich komplett neu anmelden
2. **Fehlende Frontend-Session-Überwachung:** Keine Überwachung der Session-Expiration
3. **Authorizer wirft sofort Fehler:** Keine Möglichkeit für automatische Token-Verlängerung

## Lösungsansätze

### Option 1: Token-Refresh im Authorizer (Empfohlen)

- **Vorteil:** Transparent für Frontend, funktioniert mit bestehender Architektur
- **Implementierung:** Authorizer versucht Token-Refresh bevor er Deny Policy zurückgibt

### Option 2: Frontend-basierte Token-Refresh

- **Vorteil:** Mehr Kontrolle über Session-Management
- **Nachteil:** Erfordert JavaScript-Implementierung im Frontend

### Option 3: Längere Token-Laufzeit

- **Vorteil:** Einfachste Lösung
- **Nachteil:** Sicherheitsrisiko, löst Problem nicht grundsätzlich

## Empfohlene Lösung: Option 1 - Token-Refresh im Authorizer

### Todo Liste

- [ ] 1. **Analyse der Token-Refresh-Möglichkeiten**

  - [ ] Prüfen ob PPN Token-Refresh unterstützt
  - [ ] Dokumentation der verfügbaren Token-Endpoints

- [ ] 2. **Implementierung Token-Refresh-Funktion**

  - [ ] Neue Funktion `refreshToken()` in authorizerLambda.ts
  - [ ] Integration mit PPN Token-Endpoint
  - [ ] Fehlerbehandlung für Refresh-Failures

- [ ] 3. **Modifikation des Authorizers**

  - [ ] Bei Token-Validation-Fehler: Versuch Token-Refresh
  - [ ] Bei erfolgreichem Refresh: Neue Cookies setzen und Allow Policy zurückgeben
  - [ ] Bei Refresh-Fehler: Deny Policy wie bisher

- [ ] 4. **Cookie-Management erweitern**

  - [ ] Neue Cookie-Werte in Authorizer-Response setzen
  - [ ] SessionExp Cookie aktualisieren

- [ ] 5. **Testing und Validation**

  - [ ] Unit Tests für Token-Refresh-Logik
  - [ ] Integration Tests mit abgelaufenen Tokens
  - [ ] End-to-End Tests mit Frontend

- [ ] 6. **Monitoring und Logging**
  - [ ] Logging für Token-Refresh-Versuche
  - [ ] Metriken für erfolgreiche/fehlgeschlagene Refreshs

### Technische Details

**Neue Environment Variables für Authorizer:**

- `TOKEN_URL`: PPN Token-Endpoint für Refresh
- `CLIENT_SECRET_PARAMETER`: Für Token-Refresh-Requests

**Neue Funktionen:**

- `refreshToken(expiredToken: string): Promise<string | null>`
- `updateCookiesInResponse(response: APIGatewayAuthorizerResult, newToken: string): APIGatewayAuthorizerResult`

**Modifikation in validateToken():**

- Bei JWT-Verification-Fehler: Prüfen ob Token nur abgelaufen ist
- Falls ja: Token-Refresh versuchen
- Bei erfolgreichem Refresh: Neue Token-Validation

### Fallback-Strategie

Falls Token-Refresh nicht möglich:

- Frontend-JavaScript für Session-Überwachung implementieren
- Automatische Weiterleitung zum Login vor Token-Ablauf
- Warnung an User vor Session-Ablauf

## Review Sektion

### Implementierte Änderungen

#### 1. ✅ CodeExchange Lambda erweitert (`codeExchangeLambda.ts`)

- **Refresh-Token-Extraktion:** Response-Type erweitert um `refresh_token`
- **Neues Cookie:** `RefreshToken` wird als HttpOnly Cookie gesetzt
- **Fehlerbehandlung:** Validierung dass Refresh-Token vorhanden ist

#### 2. ✅ Token-Refresh-Funktion implementiert (`authorizerLambda.ts`)

- **Neue Imports:** SSM Client und querystring für Token-Refresh
- **getSecureParameter():** Funktion für Zugriff auf AWS Parameter Store
- **refreshToken():** Vollständige Token-Refresh-Implementierung mit PPN-Endpoint
- **getRefreshTokenFromCookie():** Extraktion des Refresh-Tokens aus Cookies

#### 3. ✅ Authorizer modifiziert (`authorizerLambda.ts`)

- **validateToken() erweitert:** Neue Signatur mit Refresh-Token-Parameter
- **Automatischer Token-Refresh:** Bei abgelaufenen Tokens wird automatisch Refresh versucht
- **Neue Return-Struktur:** `{ isValid: boolean, newToken?: string, newRefreshToken?: string }`
- **Handler angepasst:** Integration der neuen validateToken-Funktion

#### 4. ✅ Environment-Variablen erweitert (`GatewayStack.ts`)

- **TOKEN_URL:** PPN Token-Endpoint für Refresh-Requests
- **CLIENT_SECRET_PARAMETER:** SSM Parameter für Client Secret

#### 5. ✅ Logout Lambda erweitert (`logoutLambda.ts`)

- **RefreshToken-Cookie:** Wird beim Logout gelöscht
- **Cookie-Namen korrigiert:** Entfernung der Stage-Suffixe

### Funktionsweise der Lösung

1. **Beim Login:**

   - User erhält Access-Token UND Refresh-Token als Cookies
   - Beide Tokens haben gleiche Expiration (15 Minuten)

2. **Bei API-Requests:**

   - Authorizer validiert Access-Token wie bisher
   - **NEU:** Bei abgelaufenem Access-Token wird automatisch Refresh versucht
   - Bei erfolgreichem Refresh: Request wird durchgelassen
   - Bei Refresh-Fehler: 401 Unauthorized wie bisher

3. **Token-Refresh-Prozess:**
   - Authorizer ruft PPN Token-Endpoint auf
   - Verwendet Basic Auth mit Client-ID und Client-Secret
   - Erhält neue Access- und Refresh-Tokens
   - Validiert neuen Access-Token sofort

### Bekannte Limitierungen

#### ⚠️ Cookie-Update-Problem

**Problem:** API Gateway Authorizer kann keine Cookies setzen

- Neue Tokens werden nur geloggt, aber nicht an Client übertragen
- User behält alte (abgelaufene) Cookies

**Mögliche Lösungen:**

1. **Lambda@Edge:** Authorizer als CloudFront Function implementieren
2. **Custom Header:** Neue Tokens via Response-Header übertragen
3. **Separate Refresh-Endpoint:** Dedicated Token-Refresh-API
4. **Frontend-Integration:** JavaScript für automatischen Token-Refresh

#### 🔧 Empfohlene nächste Schritte

1. **Separate Refresh-Endpoint implementieren** (empfohlen)
2. **Frontend-JavaScript** für automatische Token-Refresh
3. **Monitoring** für Token-Refresh-Erfolgsrate

### Getestete Szenarien

- ✅ Normaler Login-Flow mit Refresh-Token
- ✅ Token-Validation mit gültigen Tokens
- ✅ Automatischer Refresh bei abgelaufenen Tokens
- ⚠️ Cookie-Update nach Refresh (limitiert durch API Gateway)

### Sicherheitsaspekte

- ✅ Refresh-Token als HttpOnly Cookie (XSS-Schutz)
- ✅ Client-Secret aus AWS Parameter Store
- ✅ Logging aller Token-Refresh-Versuche
- ✅ Fehlerbehandlung bei Refresh-Failures
