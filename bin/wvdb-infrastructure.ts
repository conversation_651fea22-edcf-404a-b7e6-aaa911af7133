import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { KMSStack } from '../lib/KMSStack';
import { DynamoDBStack } from '../lib/DynamoDBStack';
import { VPCStack } from '../lib/VPCStack';
import { GatewayStack } from '../lib/GatewayStack';
import { FrontendBucketStack } from '../lib/FrontendBucketStack';
import { SSMStack } from '../lib/SSMStack';
import { SNSStack } from '../lib/SNSStack';
import { EventBridgeStack } from '../lib/EventbridgeStack';
import { CloudFrontWAFStack } from '../lib/CloudfrontWafStack';
import { CloudFrontStack } from '../lib/CloudfrontStack';
import { CertificateStack } from '../lib/CloudfrontCertStack';
const app = new cdk.App();

const stage = app.node.tryGetContext('stage');
const usecase = app.node.tryGetContext('usecase') || 'wvdb';
const region = app.node.tryGetContext('region') || 'eu-west-1';
const replicaRegion = app.node.tryGetContext('replicaRegion') || 'eu-central-1';
const dynamoReplicaKmsId = app.node.tryGetContext('dynamoReplicaKmsId') || '';
const ppnClientSecret = app.node.tryGetContext('ppnClientSecret') || '';
const ppnMasterClientSecret = app.node.tryGetContext('ppnMasterClientSecret') || '';
const ppnU2AClientSecret = app.node.tryGetContext('ppnU2AClientSecret') || '';

let hostedZoneName: string;
let hostedZoneId: string;
let clientId: string;
let u2aClientId: string;
let masterDataUrl: string;
let masterDataClientId: string;
let importerRoleCode: string;
let issuer: string;
let publicKeyUrl: string;
let tokenUrl: string;
let loginUrl: string;
let basePPNUrl: string;

if (stage === 'prod') {
  hostedZoneName = `UNDEFINED`;
  hostedZoneId = 'UNDEFINED';
  clientId = "5d979e1a-cc02-461b-991e-aa2fb0e26ae6";
  u2aClientId = 'f2a32947-7127-42a7-97a1-e53f60d7616a';
  masterDataUrl = 'https://eu-0.api.porsche.io/porsche-group/prod/ppn/masterdata';
  masterDataClientId = '356881fc121b996be5ae5b395beb40a3';
  importerRoleCode = '343932323433343037';
  issuer = 'https://ppn.porsche.com';
  publicKeyUrl = 'https://ppn.porsche.com/pf/JWKS';
  tokenUrl = 'https://ppn.porsche.com/as/token.oauth2';
  loginUrl = 'https://ppn.porsche.com/as/authorization.oauth2';
  basePPNUrl = 'https://ppn.porsche.com/';
} else {
  hostedZoneName = `wiederverkaeuferdatenbankdev.aws.platform.porsche-preview.cloud`;
  hostedZoneId = 'Z09986023S13X00H7KRBV';
  clientId = "1ec09283-f2da-4bb9-8a1a-e3c6514392f2";
  u2aClientId = 'f79cb5c7-0d1f-4f44-8bb1-8e484be09c20';
  masterDataUrl = 'https://eu-0.test.api.porsche.io/porsche-group/test/ppn/masterdata';
  masterDataClientId = 'e119a5ccf47d6afc4c706ff6c60e4525';
  importerRoleCode = '34393932313638373937';
  issuer = 'https://ppnlite.porsche.com';
  publicKeyUrl = 'https://ppnlite.porsche.com/pf/JWKS';
  tokenUrl = 'https://ppnlite.porsche.com/as/token.oauth2';
  loginUrl = 'https://ppnlite.porsche.com/as/authorization.oauth2';
  basePPNUrl = 'https://ppnlite.porsche.com/';
}
createStacks();

function createStacks() {
  const kmsStack = new KMSStack(app, stage + '-' + usecase + '-' + 'KMSStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
  });

  const kmsStackUsEast = new KMSStack(app, stage + '-' + usecase + '-' + 'UsEastKMSStack', {
    env: { region: 'us-east-1' },
    stage: stage,
    usecase: usecase,
  });

  const dynamoDBStack = new DynamoDBStack(app, stage + '-' + usecase + '-' + 'DynamoDBStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
    replicaRegion: replicaRegion,
    replicaKmsId: dynamoReplicaKmsId,
    replicationNeeded: 'false',
  });
  dynamoDBStack.addDependency(kmsStack);

  const vpcStack = new VPCStack(app, stage + '-' + usecase + '-' + 'VPCStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
  });
  vpcStack.addDependency(kmsStack);

  const snsStack = new SNSStack(app, stage + '-' + usecase + '-' + 'SNSStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
  });
  snsStack.addDependency(kmsStack);

  const ssmStack = new SSMStack(app, stage + '-' + usecase + '-' + 'SSMStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
    ppnClientSecret: ppnClientSecret,
    ppnMasterClientSecret: ppnMasterClientSecret,
    ppnU2AClientSecret: ppnU2AClientSecret,
  });
  ssmStack.addDependency(kmsStack);

  const eventBridgeStack = new EventBridgeStack(app, stage + '-' + usecase + '-' + 'EventBridgeStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
  });
  eventBridgeStack.addDependency(snsStack);

  const frontendBucketStack = new FrontendBucketStack(app, stage + '-' + usecase + '-' + 'FrontendBucketStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
  });
  frontendBucketStack.addDependency(kmsStack);

  const gatewayStack = new GatewayStack(app, stage + '-' + usecase + '-' + 'GatewayStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,

    hostedZoneName: hostedZoneName,
    hostedZoneId: hostedZoneId,
    issuer: issuer,
    publicKeyUrl: publicKeyUrl,
    clientId: clientId,
    tokenUrl: tokenUrl,
    loginUrl: loginUrl,
    basePPNUrl: basePPNUrl,
    u2aClientId: u2aClientId,
    masterDataUrl: masterDataUrl,
    masterDataClientId: masterDataClientId,
    importerRoleCode: importerRoleCode,
  });
  gatewayStack.addDependency(kmsStack);
  gatewayStack.addDependency(frontendBucketStack);

  const cloudfrontWafStack = new CloudFrontWAFStack(app, stage + '-' + usecase + '-' + 'CloudfrontWafStack', {
    env: { region: 'us-east-1' },
    crossRegionReferences: true,
    stage: stage,
    usecase: usecase,
  });
  cloudfrontWafStack.addDependency(gatewayStack);

  const certStack = new CertificateStack(app, stage + '-' + usecase + '-' + 'CloudfrontCertStack', {
    env: { region: 'us-east-1' },
    crossRegionReferences: true,
    stage: stage,
    usecase: usecase,
    hostedZoneId: hostedZoneId,
    zoneName: hostedZoneName,
  });

  const cloudFrontStack = new CloudFrontStack(app, stage + '-' + usecase + '-' + 'CloudfrontStack', {
    env: { region: region },
    stage: stage,
    usecase: usecase,
    hostedZoneId: hostedZoneId,
    zoneName: hostedZoneName,
  });
  cloudFrontStack.addDependency(certStack);
  cloudFrontStack.addDependency(cloudfrontWafStack);


}
