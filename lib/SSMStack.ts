import * as cdk from "aws-cdk-lib";
import * as cfninc from "aws-cdk-lib/cloudformation-include";
import { Construct } from "constructs";
import { join } from "path";

export interface SSMStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
    ppnClientSecret: string;
    ppnMasterClientSecret: string;
    ppnU2AClientSecret: string;
}

export class SSMStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: SSMStackProps) {
        super(scope, id, props);

        new cfninc.CfnInclude(this, "SSMStack", {
            templateFile: join(__dirname, "../cfntemplates/ssm.yml"),
            parameters: {
                Stage: props.stage,
                UseCase: props.usecase,
                PPNClientSecret: props.ppnClientSecret,
                PPNMasterClientSecret: props.ppnMasterClientSecret,
                PPNU2AClientSecret: props.ppnU2AClientSecret,
            },
        });
    }
}
