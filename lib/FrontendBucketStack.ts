import { CfnOutput } from 'aws-cdk-lib';
import * as cdk from "aws-cdk-lib";
import { Construct } from 'constructs';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';


export interface FrontendBucketStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
}

export class FrontendBucketStack extends cdk.Stack {
    public constructor(scope: Construct, id: string, props: FrontendBucketStackProps) {
        super(scope, id, props);

        const account = cdk.Stack.of(this).account;
        const region = cdk.Stack.of(this).region;

        const logBucket = s3.Bucket.fromBucketName(this, `porsche-compliance-log-${account}-${region}`, `porsche-compliance-log-${account}-${region}`);

        const importedKeyArn = cdk.Fn.importValue(`${props.stage}-${props.usecase}-s3-cmk-key-arn`);
        const cmkKey = kms.Key.fromKeyArn(this, `${props.stage}-${props.usecase}-s3-cmk`, importedKeyArn);

        const frontendBucket = new s3.Bucket(this, props.stage + '-' + props.usecase + '-frontend-bucket', {
            bucketName: props.stage + '-' + props.usecase + '-frontend-bucket',
            encryption: s3.BucketEncryption.KMS,
            encryptionKey: cmkKey,
            bucketKeyEnabled: true,
            publicReadAccess: false,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
            serverAccessLogsBucket: logBucket,
            serverAccessLogsPrefix: 'website-access-logs/',
        });

        const bucketPolicy = new iam.PolicyStatement({
            actions: ['s3:*'],
            resources: [frontendBucket.bucketArn, `${frontendBucket.bucketArn}/*`],
            principals: [new iam.AnyPrincipal()],
            effect: iam.Effect.DENY,
            conditions: {
                Bool: {
                    'aws:SecureTransport': 'false',
                },
            },
        });
        frontendBucket.addToResourcePolicy(bucketPolicy);

        const denyUnencryptedUploads = new iam.PolicyStatement({
            effect: iam.Effect.DENY,
            actions: ['s3:PutObject'],
            principals: [new iam.AnyPrincipal()],
            resources: [frontendBucket.bucketArn + '/*'],
            conditions: {
                StringNotEquals: {
                    's3:x-amz-server-side-encryption': 'aws:kms',
                },
            },
        });
        frontendBucket.addToResourcePolicy(denyUnencryptedUploads);

        const denyWithoutEncryptionHeader = new iam.PolicyStatement({
            effect: iam.Effect.DENY,
            actions: ['s3:PutObject'],
            principals: [new iam.AnyPrincipal()],
            resources: [frontendBucket.bucketArn + '/*'],
            conditions: {
                Null: {
                    's3:x-amz-server-side-encryption': 'true',
                },
            },
        });
        frontendBucket.addToResourcePolicy(denyWithoutEncryptionHeader);

        new CfnOutput(this, 'FrontendBucket', {
            value: frontendBucket.bucketName,
            exportName: `${props.stage}-${props.usecase}-frontend-bucket`,
        });
    }
}
