import * as cdk from 'aws-cdk-lib/core';
import { Stack, StackProps } from 'aws-cdk-lib/core';
import { Construct } from 'constructs';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as cloudfrontOrigins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import { HostedZone } from 'aws-cdk-lib/aws-route53';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { SSMParameterReader } from './ssm-parameter-reader';


export interface CloudFrontStackProps extends StackProps {
    stage: string;
    usecase: string;
    hostedZoneId: string;
    zoneName: string;
}

export class CloudFrontStack extends Stack {
    private hostedZone: route53.IHostedZone;

    constructor(scope: Construct, id: string, props: CloudFrontStackProps) {
        super(scope, id,);

        const accountId = Stack.of(this).account;
        const region = Stack.of(this).region;

        const hz = HostedZone.fromHostedZoneAttributes(this, 'HostedZone', {
            hostedZoneId: props.hostedZoneId,
            zoneName: props.zoneName,
        });
        this.hostedZone = hz;

        const apigatewayDomainName = cdk.Fn.importValue(`${props.stage}-${props.usecase}-apigw-domain-name`);

        const certificateArnReader = new SSMParameterReader(this, 'CertificateARNReader', {
            parameterName: "CERTIFICATE_ARN",
            region: 'us-east-1'
        });

        const webAclIdReader = new SSMParameterReader(this, 'WebAclARNReader', {
            parameterName: "WEBACL_ID",
            region: 'us-east-1'
        });

        const complianceBucket = Bucket.fromBucketName(this, 'compliance-bucket', `porsche-compliance-log-${accountId}-${region}`);
        // // Create cache policy
        // // const cachePolicy = new cloudfront.CachePolicy(this, 'CloudFrontCachePolicy', {
        // //   cachePolicyName: `${props.stage}-${props.usecase}-cloudfront-cache-policy`,
        // //   defaultTtl: cdk.Duration.minutes(0),
        // //   maxTtl: cdk.Duration.minutes(0),
        // //   headerBehavior: cloudfront.CacheHeaderBehavior.none(),
        // //   queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
        // //   cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        // // });

        // const cachePolicy = new cloudfront.CachePolicy(this, 'CloudFrontCachePolicy', {
        //   cachePolicyName: `${props.stage}-${props.usecase}-cloudfront-cache-policy`,
        //   comment: 'Cache policy for web application',
        //   defaultTtl: cdk.Duration.days(1), // Default time to live for cached contents (1 day)
        //   minTtl: cdk.Duration.seconds(0), // Minimum time to live for cached contents
        //   maxTtl: cdk.Duration.days(365), // Maximum time to live for cached contents
        //   cookieBehavior: cloudfront.CacheCookieBehavior.none(), // No cookies will be cached
        //   headerBehavior: cloudfront.CacheHeaderBehavior.none(), // No headers will be cached
        //   queryStringBehavior: cloudfront.CacheQueryStringBehavior.all(), // Cache all query strings
        //   enableAcceptEncodingGzip: true, // Enable Gzip compression
        //   enableAcceptEncodingBrotli: true, // Enable Brotli compression
        // });

        // Create a custom origin request policy
        const originRequestPolicy = new cloudfront.OriginRequestPolicy(this, 'CloudFrontOriginRequestPolicy', {
            originRequestPolicyName: `${props.stage}-${props.usecase}-cloundfront-origin-request-policy`,
            headerBehavior: cloudfront.OriginRequestHeaderBehavior.all(),
            queryStringBehavior: cloudfront.OriginRequestQueryStringBehavior.all(),
            cookieBehavior: cloudfront.OriginRequestCookieBehavior.all(),
        });

        const distribution = new cloudfront.Distribution(this, 'CloudFrontApiDistribution', {
            defaultBehavior: {
                origin: new cloudfrontOrigins.HttpOrigin(apigatewayDomainName),
                cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
                // cachePolicy: cachePolicy,
                originRequestPolicy: originRequestPolicy,
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
            },
            domainNames: [`${props.zoneName}`],
            certificate: acm.Certificate.fromCertificateArn(this, 'ImportedCert', certificateArnReader.getParameterValue()),
            logBucket: complianceBucket,
            logIncludesCookies: true,
            logFilePrefix: `cloudfront`,
            webAclId: webAclIdReader.getParameterValue(),
        });

        // Update Route 53 record to point to CloudFront distribution
        new route53.ARecord(this, 'CloudFrontAliasRecord', {
            zone: this.hostedZone,
            target: route53.RecordTarget.fromAlias(new route53targets.CloudFrontTarget(distribution)),
        });
    }
}
