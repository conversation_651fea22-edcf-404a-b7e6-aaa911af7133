import * as cdk from 'aws-cdk-lib';
import * as cfninc from 'aws-cdk-lib/cloudformation-include';
import { Construct } from 'constructs';
import { join } from 'path';

export interface DynamoDBStackProps extends cdk.StackProps {
  stage: string;
  usecase: string;
  replicationNeeded: string;
  replicaRegion: string;
  replicaKmsId?: string;
}

export class DynamoDBStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: DynamoDBStackProps) {
    super(scope, id, props);

    new cfninc.CfnInclude(this, 'DynamoDBStack', {
      templateFile: join(__dirname, '../cfntemplates/dynamodb.yml'),
      parameters: {
        Stage: props.stage,
        UseCase: props.usecase,
        PrimaryRegion: props.env?.region,
        ReplicaRegion: props.replicaRegion,
        ReplicaKmsId: props.replicaKmsId,
        ReplicationNeeded: props.replicationNeeded,
      },
    });
  }
}
