import * as cdk from "aws-cdk-lib";
import * as cfninc from "aws-cdk-lib/cloudformation-include";
import { Construct } from "constructs";
import { join } from "path";

export interface SNSStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
}

export class SNSStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: SNSStackProps) {
        super(scope, id, props);

        new cfninc.CfnInclude(this, "SNSStack", {
            templateFile: join(__dirname, "../cfntemplates/sns.yml"),
            parameters: {
                Stage: props.stage,
                UseCase: props.usecase,
            },
        });
    }
}
