import * as cdk from 'aws-cdk-lib';
import { Tags } from 'aws-cdk-lib';
import * as cfninc from 'aws-cdk-lib/cloudformation-include';
import { Construct } from 'constructs';
import { join } from 'path';
import * as ssm from 'aws-cdk-lib/aws-ssm';

export interface CloudFrontWAFStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
}

export class CloudFrontWAFStack extends cdk.Stack {
    public readonly webAclArn: string;

    constructor(scope: Construct, id: string, props: CloudFrontWAFStackProps) {
        super(scope, id, {
            env: { region: 'us-east-1' },
            crossRegionReferences: true,
        });

        const wafTemplate = new cfninc.CfnInclude(this, 'WAFStack', {
            templateFile: join(__dirname, '../cfntemplates/cloudfront-waf.yml'),
            parameters: {
                Stage: props.stage,
                UseCase: props.usecase,
            },
        });

        this.webAclArn = wafTemplate.getResource('CloudFrontWebACL').getAtt('Arn').toString();

        new ssm.StringParameter(this, 'WebAclID', {
            parameterName: "WEBACL_ID",
            description: 'Web ACL ID',
            stringValue: this.webAclArn
        });

    }
}
