import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as cdk from "aws-cdk-lib";
import * as ssm from 'aws-cdk-lib/aws-ssm';



export interface CertificateStackProps extends StackProps {
    stage: string,
    usecase: string,
    hostedZoneId: string;
    zoneName: string;
}

export class CertificateStack extends Stack {
    public readonly certificateArn: string;

    constructor(scope: Construct, id: string, props: CertificateStackProps) {
        super(scope, id, {
            env: { region: 'us-east-1' },
            crossRegionReferences: true,
        });

        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'HostedZone', {
            hostedZoneId: props.hostedZoneId,
            zoneName: props.zoneName,
        });

        const certificate = new acm.Certificate(this, 'DomainCertificate', {
            domainName: props.zoneName,
            validation: acm.CertificateValidation.fromDns(hostedZone),
        });

        this.certificateArn = certificate.certificateArn;


        new ssm.StringParameter(this, 'CertificateARN', {
            parameterName: "CERTIFICATE_ARN",
            description: 'Certificate ARN to be used with Cloudfront',
            stringValue: this.certificateArn
        });

    }
}
