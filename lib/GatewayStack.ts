import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as iam from 'aws-cdk-lib/aws-iam';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { CfnOutput } from 'aws-cdk-lib';
import { RetentionDays, LogGroup } from 'aws-cdk-lib/aws-logs';
import { LogGroupLogDestination } from 'aws-cdk-lib/aws-apigateway';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { InternalDeveloperFirewallConstruct } from './WAFStack';
import { RemovalPolicy } from 'aws-cdk-lib/core';


export interface GatewayStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
    hostedZoneName: string;
    hostedZoneId: string;
    issuer: string;
    publicKeyUrl: string;
    clientId: string;
    tokenUrl: string;
    loginUrl: string;
    basePPNUrl: string;
    u2aClientId: string,
    masterDataUrl: string,
    masterDataClientId: string,
    importerRoleCode: string,
}

export class GatewayStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: GatewayStackProps) {
        super(scope, id, props);

        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, `HostedZone`, {
            hostedZoneId: props.hostedZoneId,
            zoneName: props.hostedZoneName,
        });

        const cloudWatchKmsKeyArnExportName = `${props.stage}-${props.usecase}-cloudwatch-cmk-key-arn`;
        const logGroupKmsKeyArn = cdk.Fn.importValue(cloudWatchKmsKeyArnExportName);
        const logGroupKey = kms.Key.fromKeyArn(this, `logGroupKey`, logGroupKmsKeyArn);

        // Create a certificate for the domain
        const domainName = `${props.hostedZoneName}`;
        const certificate = new acm.Certificate(this, `Certificate`, {
            domainName: domainName,
            validation: acm.CertificateValidation.fromDns(hostedZone),
        });

        const customDomain = new apigateway.DomainName(this, 'ApiCustomDomain', {
            domainName: domainName,
            certificate,
            endpointType: apigateway.EndpointType.REGIONAL,
            securityPolicy: apigateway.SecurityPolicy.TLS_1_2,
        });


        this.createGateway(props, 'frontend', logGroupKey, domainName, certificate, hostedZone, customDomain);
        this.createGateway(props, 'backend', logGroupKey, domainName, certificate, hostedZone, customDomain);

        new cdk.CfnOutput(this, 'ApiGatewayDomainNameOutput', {
            value: customDomain.domainNameAliasDomainName,
            exportName: `${props.stage}-${props.usecase}-apigw-domain-name`,
        });

        // new route53.ARecord(this, `ApiGatewayAliasRecord`, {
        //     zone: hostedZone,
        //     target: route53.RecordTarget.fromAlias(new route53targets.ApiGatewayDomain(customDomain)),
        //     ttl: cdk.Duration.minutes(5),
        // });
    }



    private createGateway(props: GatewayStackProps, gatewayType: string, logGroupKey: kms.IKey, domainName: string, certificate: acm.ICertificate, hostedZone: route53.IHostedZone, customDomain: cdk.aws_apigateway.DomainName) {

        const apiGwLogGroup = new LogGroup(this, `${gatewayType}ApiGatewayAccessLogs`, {
            logGroupName: `/aws/apigateway/${props.stage}-${props.usecase}-${gatewayType}`,
            retention: RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
            encryptionKey: logGroupKey,
        });


        // Create the API Gateway
        const api = new apigateway.RestApi(this, `${gatewayType}ApiGateway`, {
            restApiName: `${props.stage}-${props.usecase}-${gatewayType}`,
            description: `Website API Gateway for WVDB ${props.stage}`,
            binaryMediaTypes: ['image/*'],
            cloudWatchRole: true,
            cloudWatchRoleRemovalPolicy: RemovalPolicy.DESTROY,
            deployOptions: {
                stageName: props.stage,
                tracingEnabled: true,
                throttlingRateLimit: 10000,
                throttlingBurstLimit: 5000,
                metricsEnabled: true,
                loggingLevel: apigateway.MethodLoggingLevel.INFO,
                dataTraceEnabled: true,
                accessLogDestination: new LogGroupLogDestination(apiGwLogGroup),
                accessLogFormat: apigateway.AccessLogFormat.custom(
                    `{"requestId":"$context.requestId","waf-error":"$context.waf.error","waf-status":"$context.waf.status","waf-latency":"$context.waf.latency","waf-response":"$context.waf.wafResponseCode","authenticate-error":"$context.authenticate.error","authenticate-status":"$context.authenticate.status","authenticate-latency":"$context.authenticate.latency","integration-error":"$context.integration.error","integration-status":"$context.integration.status","integration-latency":"$context.integration.latency","integration-requestId":"$context.integration.requestId","integration-integrationStatus":"$context.integration.integrationStatus","response-latency":"$context.responseLatency","ip":"$context.identity.sourceIp","caller":"$context.identity.caller","user":"$context.identity.user","arn":"$context.identity.userArn","account":"$context.identity.accountId","requestTime":"$context.requestTime","httpMethod":"$context.httpMethod","resourcePath":"$context.resourcePath","status":"$context.status","message":"$context.error.message","protocol":"$context.protocol","responseLength":"$context.responseLength","user-agent":"$context.identity.userAgent","referer":"$input.params('Referer')","origin":"$input.params('Origin')"}`,
                ),
            },

            policy: new iam.PolicyDocument({
                statements: [
                    new iam.PolicyStatement({
                        actions: ['execute-api:Invoke'],
                        resources: ['*'],
                        effect: iam.Effect.ALLOW,
                        principals: [new iam.AnyPrincipal()],
                    }),
                ],
            }),
        });
        api.node.addDependency(apiGwLogGroup);

        new LogGroup(this, `${gatewayType}ApiGatewayExecutionLogGroup`, {
            logGroupName: `API-Gateway-Execution-Logs_${api.restApiId}/${props.stage}`,
            retention: RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
            encryptionKey: logGroupKey,
        });

        let basePath = '';
        if (gatewayType === 'backend') {
            basePath = 'api';
        }

        new apigateway.BasePathMapping(this, `${gatewayType}ApiBasePathMapping`, {
            domainName: customDomain,
            restApi: api,
            basePath: basePath
        });


        const authorizerEnvironment = {
            ISSUER: props.issuer,
            PUBLIC_KEY_ENDPOINT: props.publicKeyUrl,
            CLIENT_ID: props.clientId,
            //  PPN_ROLES_WRITE: JSON.stringify(props.ppnRolesWrite),
            STAGE: props.stage,
        };
        const authorizerLambda = this.createNodeJSLambda('authorizer', props, logGroupKey, gatewayType, authorizerEnvironment);
        let cachingSeconds = 0;
        if (gatewayType === 'frontend') {
            cachingSeconds = 300;
        }
        // Create RequestAuthorizer for the API Gateway
        // Note that the identity source is the 'Cookie' header and not the 'Authorization' header
        // This is because there is no option to validate only parts of the Cookie header in API Gateway
        const authorizer = new apigateway.RequestAuthorizer(this, `${gatewayType}Authorizer`, {
            handler: authorizerLambda,
            identitySources: [apigateway.IdentitySource.header('Cookie')], // Use the 'Cookie' header as the identity source
            resultsCacheTtl: cdk.Duration.seconds(cachingSeconds),
        });

        if (gatewayType === 'frontend') {

            const publicAuthorizerLambda = this.createNodeJSLambda('publicAuthorizer', props, logGroupKey, gatewayType, authorizerEnvironment);


            const publicLambdaAuthorizer = new apigateway.RequestAuthorizer(this, `PublicEndpointLambdaAuthorizer`, {
                authorizerName: 'PublicEndpointLambdaAuthorizer',
                handler: publicAuthorizerLambda,
                identitySources: [apigateway.IdentitySource.header('User-Agent')],
            });


            // const secretsManagerKmsKeyArnExportName = `${props.stage}-${props.usecase}-secretsmanager-cmk-key-arn`;
            // const secretsManagerKmsKeyArn = cdk.Fn.importValue(secretsManagerKmsKeyArnExportName);
            // const secretsManagerKey = kms.Key.fromKeyArn(this, 'secretsManagerKey', secretsManagerKmsKeyArn);

            // const secretArn = cdk.Fn.importValue(`${props.stage}-${props.usecase}-client-secret`);

            // const secret = secretsmanager.Secret.fromSecretAttributes(this, `${gatewayType}IdpClientSecret`, {
            //     encryptionKey: secretsManagerKey,
            //     secretCompleteArn: secretArn,
            // });

            const bucketName = cdk.Fn.importValue(`${props.stage}-${props.usecase}-frontend-bucket`);
            const frontendBucket = s3.Bucket.fromBucketName(this, 'ImportedFrontendBucket', bucketName);

            // Create an IAM role for API Gateway
            const executeRole = new iam.Role(this, `${gatewayType}ApiGatewayS3AssumeRole`, {
                assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
            });

            // Grant the necessary permissions to access the S3 bucket
            frontendBucket.grantRead(executeRole);

            const logoutEnvironment = {
                REDIRECT_URL: `https://ppnlite.porsche.com/idp/startSLO.ping`, // redirect to PPN after logout
                COOKIE_DOMAIN: domainName,
                STAGE: props.stage,
            };
            const logoutLambda = this.createNodeJSLambda('logout', props, logGroupKey, gatewayType, logoutEnvironment);

            const codeExchangeEnvironment = {
                USER_POOL_CLIENT_ID: props.clientId,
                REDIRECT_URI: `https://${domainName}/auth`,
                //CLIENT_SECRET_PARAMETER: secretArn,
                CLIENT_SECRET_PARAMETER: `${props.stage}-${props.usecase}-ppn-client-secret`,
                TOKEN_URL: props.tokenUrl,
                LOGIN_URL: props.loginUrl,
                PPN_BASE_URL: props.basePPNUrl,
                COOKIE_DOMAIN: domainName,
                STAGE: props.stage,
                U2A_CLIENT_ID: props.u2aClientId,
                MASTER_DATA_URL: props.masterDataUrl,
                MASTER_DATA_CLIENT_ID: props.masterDataClientId,
                MASTER_DATA_CLIENT_SECRET_PARAMETER: `${props.stage}-${props.usecase}-ppn-master-client-secret`,
                U2A_CLIENT_SECRET_PARAMETER: `${props.stage}-${props.usecase}-ppn-u2a-client-secret`,
                IMPORTER_ROLE_CODE: props.importerRoleCode,
            };

            const codeExchangeLambda = this.createNodeJSLambda('codeExchange', props, logGroupKey, gatewayType, codeExchangeEnvironment);

            // Create an API Gateway integration with the S3 service to serve the 'index.html' file from the frontend bucket.
            const s3IndexIntegration = new apigateway.AwsIntegration({
                service: 's3',
                integrationHttpMethod: 'GET',
                path: `${frontendBucket.bucketName}/index.html`,
                options: {
                    credentialsRole: executeRole,
                    integrationResponses: [
                        {
                            statusCode: '200',
                            responseParameters: {
                                'method.response.header.Content-Type': 'integration.response.header.Content-Type',
                            },
                        },
                    ],
                },
            });

            // Add a 'GET' method to the API Gateway root resource that uses the 's3IndexIntegration' for serving the 'index.html' file.
            api.root.addMethod('GET', s3IndexIntegration, {
                methodResponses: [
                    {
                        statusCode: '200',
                        responseParameters: {
                            'method.response.header.Content-Type': true,
                        },
                    },
                ],
                requestParameters: {
                    'method.request.header.Content-Type': true,
                },
                authorizer,
            });

            // Create another API Gateway integration with the S3 service to serve all other files from the frontend bucket.
            const s3Integration = new apigateway.AwsIntegration({
                service: 's3',
                integrationHttpMethod: 'GET',
                path: `${frontendBucket.bucketName}/{proxy}`,
                options: {
                    credentialsRole: executeRole,
                    integrationResponses: [
                        {
                            statusCode: '200',
                            responseParameters: {
                                'method.response.header.Content-Type': 'integration.response.header.Content-Type',
                            },
                        },
                    ],
                    requestParameters: {
                        'integration.request.path.proxy': 'method.request.path.proxy',
                    },
                },
            });

            // Add a new resource with a '{proxy+}' path variable to the API Gateway root resource and associate it with the 's3Integration'.
            api.root.addResource('{proxy+}').addMethod('GET', s3Integration, {
                methodResponses: [
                    {
                        statusCode: '200',
                        responseParameters: {
                            'method.response.header.Content-Type': true,
                        },
                    },
                ],
                requestParameters: {
                    'method.request.path.proxy': true,
                    'method.request.header.Content-Type': true,
                },
                authorizer,
            });

            const integrationOptions = {
                methodResponses: [
                    {
                        statusCode: '200',
                        responseParameters: {
                            'method.response.header.Content-Type': true,
                        },
                    },
                ],
                requestParameters: {
                    'method.request.header.Content-Type': true,
                },
                authorizer: publicLambdaAuthorizer,
            };

            //secret.grantRead(codeExchangeLambda); // Grant the Lambda function access to the client secret
            // Create a new resource for the '/auth' path and catch all sub-paths
            const authResource = api.root.addResource('auth');
            const catchAllResource = authResource.addResource('{proxy+}');
            authResource.addMethod('GET', new apigateway.LambdaIntegration(codeExchangeLambda), integrationOptions);
            catchAllResource.addMethod('GET', new apigateway.LambdaIntegration(codeExchangeLambda), integrationOptions);


            // Create a new resource for the '/logout' path
            const logoutResource = api.root.addResource('logout');

            // Integration options for logout
            const logoutIntegrationOptions = {
                methodResponses: [
                    {
                        statusCode: '302',
                        responseParameters: {
                            'method.response.header.Set-Cookie': true, // Allow Set-Cookie header
                            'method.response.header.Location': true, // Allow Location header
                        },
                    },
                ],
                authorizer,
            };

            // Add a method to the logout resource associated with the logoutFunction
            logoutResource.addMethod('GET', new apigateway.LambdaIntegration(logoutLambda), logoutIntegrationOptions);

            // Create refresh token endpoint
            const refreshTokenEnvironment = {
                TOKEN_URL: props.tokenUrl,
                CLIENT_ID: props.clientId,
                CLIENT_SECRET_PARAMETER: `${props.stage}-${props.usecase}-ppn-client-secret`,
                COOKIE_DOMAIN: domainName,
                STAGE: props.stage,
            };
            const refreshTokenLambda = this.createNodeJSLambda('refreshToken', props, logGroupKey, gatewayType, refreshTokenEnvironment);

            // Create a new resource for the '/refresh-token' path
            const refreshTokenResource = api.root.addResource('refresh-token');

            // Integration options for refresh token (similar to logout)
            const refreshTokenIntegrationOptions = {
                methodResponses: [
                    {
                        statusCode: '302',
                        responseParameters: {
                            'method.response.header.Set-Cookie': true, // Allow Set-Cookie header
                            'method.response.header.Location': true, // Allow Location header
                        },
                    },
                ],
                publicLambdaAuthorizer
            };

            // Add a method to the refresh token resource (GET for simplicity)
            refreshTokenResource.addMethod('GET', new apigateway.LambdaIntegration(refreshTokenLambda), refreshTokenIntegrationOptions);


        }
        //create dummy healthcheck to prevent error with no attached resource to authorizer in backend gateway
        if (gatewayType === 'backend') {
            const healthcheckResource = api.root.addResource('healthcheck');

            healthcheckResource.addMethod('GET', new apigateway.MockIntegration({
                integrationResponses: [
                    {
                        statusCode: '200',
                        responseParameters: {
                            'method.response.header.Content-Type': "'application/json'",
                        },
                        responseTemplates: {
                            'application/json': '{"status": "OK"}',
                        },
                    },
                ],
                passthroughBehavior: apigateway.PassthroughBehavior.NEVER,
            }), {
                authorizer,
                methodResponses: [
                    {
                        statusCode: '200',
                        responseParameters: {
                            'method.response.header.Content-Type': true,
                        },
                    },
                ],
            });
        }

        new apigateway.GatewayResponse(this, `${gatewayType}AccessDeniedResponse`, {
            restApi: api,
            type: apigateway.ResponseType.ACCESS_DENIED,
            statusCode: '401',
            templates: { 'application/json': '{"message": "You are not authorized to access this application"}' },
        });

        // Create a new resource that handles the redirect to the refresh endpoint
        new apigateway.GatewayResponse(this, `${gatewayType}UnauthenticatedResponse`, {
            restApi: api,
            type: apigateway.ResponseType.UNAUTHORIZED,
            responseHeaders: {
                Location: `'https://${domainName}/refresh-token'`,
            },
            statusCode: '302',
            templates: {},
        });


        // Add a Route53 record set for the API endpoint
        // new route53.ARecord(this, `${gatewayType}ApiGatewayAliasRecord`, {
        //     zone: hostedZone,
        //     target: route53.RecordTarget.fromAlias(new route53targets.ApiGateway(api)),
        //     ttl: cdk.Duration.minutes(5),
        // });
        const wafLogGroup = new LogGroup(this, `${gatewayType}developerFirewallLogGroup`, {
            logGroupName: `aws-waf-logs-${props.stage}-${props.usecase}-${gatewayType}`,
            retention: RetentionDays.ONE_WEEK,
            encryptionKey: logGroupKey,
            removalPolicy: RemovalPolicy.DESTROY,
        });

        // WAF that blocks all requests except for the ones coming from the internal developer network
        new InternalDeveloperFirewallConstruct(this, `${gatewayType}InternalDeveloperFirewall`, api, wafLogGroup, gatewayType);

        // Output the API Gateway URL
        new CfnOutput(this, `${gatewayType}ApiGatewayURL`, {
            value: api.url,
            exportName: `${props.stage}-${props.usecase}-${gatewayType}-gateway-url`,
        });
        new CfnOutput(this, `${gatewayType}ApiGatewayId`, {
            value: api.restApiId,
            exportName: `${props.stage}-${props.usecase}-${gatewayType}-gateway-id`,
        });

        new CfnOutput(this, `${gatewayType}AuthorizerId`, {
            value: authorizer.authorizerId,
            exportName: `${props.stage}-${props.usecase}-${gatewayType}-authorizer-id`,
        });

        new cdk.CfnOutput(this, `${gatewayType}RootId`, {
            value: api.root.resourceId,
            exportName: `${props.stage}-${props.usecase}-${gatewayType}-root-id`,
        });

    }

    private createNodeJSLambda(name: string, props: GatewayStackProps, logroupKey: kms.IKey, gatewayType: string, environment: { [key: string]: any }) {

        const functionName = `${props.stage}-${props.usecase}-${gatewayType}-${name}`;
        const logGroupName = `/aws/lambda/${functionName}`;

        new LogGroup(this, `${gatewayType}${name}LogGroup`, {
            logGroupName: logGroupName,
            retention: RetentionDays.ONE_WEEK,
            encryptionKey: logroupKey,
            removalPolicy: RemovalPolicy.DESTROY,
        });

        const customManagedPolicy = new iam.ManagedPolicy(this, `${gatewayType}${name}LambdaPolicy`, {
            managedPolicyName: functionName,
            statements: [
                new iam.PolicyStatement({
                    actions: ['logs:CreateLogGroup', 'logs:CreateLogStream', 'logs:PutLogEvents'],
                    resources: [`arn:aws:logs:${this.region}:${this.account}:log-group:${logGroupName}:*`],
                }),
                new iam.PolicyStatement({
                    actions: ['kms:Decrypt', 'kms:DescribeKey'],
                    resources: [`arn:aws:kms:${this.region}:${this.account}:key/*`],
                }),
                new iam.PolicyStatement({
                    actions: ['ssm:GetParameter'],
                    resources: [`arn:aws:ssm:${this.region}:${this.account}:parameter/*`],
                }),
            ],
        });

        const lambdaRole = new iam.Role(this, `${gatewayType}${name}LambdaRole`, {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            roleName: functionName,
            managedPolicies: [customManagedPolicy],
        });

        return new lambda.Function(this, `${gatewayType}${name}Lambda`, {
            runtime: lambda.Runtime.NODEJS_22_X,
            functionName: functionName,
            role: lambdaRole,
            code: lambda.Code.fromAsset(`${__dirname}/../resources/lambda`),
            handler: `${name}Lambda.handler`,
            timeout: cdk.Duration.seconds(30),
            environment: environment,
        });

    }

}
