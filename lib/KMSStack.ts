import * as cdk from "aws-cdk-lib";
import * as cfninc from "aws-cdk-lib/cloudformation-include";
import { Construct } from "constructs";
import { join } from "path";

export interface KMSStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
}

export class KMSStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: KMSStackProps) {
        super(scope, id, props);

        new cfninc.CfnInclude(this, "KMSStack", {
            templateFile: join(__dirname, "../cfntemplates/kms.yml"),
            parameters: {
                Stage: props.stage,
                UseCase: props.usecase,
            },
        });
    }
}
