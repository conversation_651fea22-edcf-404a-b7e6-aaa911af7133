import * as cdk from 'aws-cdk-lib';
import * as cfninc from 'aws-cdk-lib/cloudformation-include';
import { Construct } from 'constructs';
import { join } from 'path';

export interface VPCStackProps extends cdk.StackProps {
  stage: string;
  usecase: string;
}

export class VPCStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: VPCStackProps) {
    super(scope, id, props);

    new cfninc.CfnInclude(this, 'VPCStack', {
      templateFile: join(__dirname, '../cfntemplates/vpc.yml'),
      parameters: {
        Stage: props.stage,
        UseCase: props.usecase,
        Region: props.env?.region,
      },
    });
  }
}
