import * as cdk from "aws-cdk-lib";
import * as cfninc from "aws-cdk-lib/cloudformation-include";
import { Construct } from "constructs";
import { join } from "path";

export interface EventBridgeStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
}

export class EventBridgeStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: EventBridgeStackProps) {
        super(scope, id, props);

        new cfninc.CfnInclude(this, "EventBridgeStack", {
            templateFile: join(__dirname, "../cfntemplates/eventbridge.yml"),
            parameters: {
                Stage: props.stage,
                UseCase: props.usecase,
            },
        });
    }
}
