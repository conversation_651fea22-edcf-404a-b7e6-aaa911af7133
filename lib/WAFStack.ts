import { Construct } from 'constructs';
import { Fn, Stack, aws_apigateway as apigateway, aws_wafv2 as waf, aws_logs as logs } from 'aws-cdk-lib';
import type { CfnWebACL } from 'aws-cdk-lib/aws-wafv2';

// Building Block WAF 6.v01
// Predefined WAF rules to be used in the WAF
export const BuildingBlockRules: CfnWebACL.RuleProperty[] = [
    {
        name: 'AWSManagedRulesCommonRuleSet',
        priority: 10,
        visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: 'AWSManagedRulesCommonRuleSet',
        },
        overrideAction: {
            none: {},
        },
        statement: {
            managedRuleGroupStatement: {
                vendorName: 'AWS',
                name: 'AWSManagedRulesCommonRuleSet',
                excludedRules: [],
            },
        },
    },
    {
        name: 'AWS-AWSManagedRulesAmazonIpReputationList',
        priority: 20,
        statement: {
            managedRuleGroupStatement: {
                vendorName: 'AWS',
                name: 'AWSManagedRulesAmazonIpReputationList',
            },
        },
        overrideAction: {
            none: {},
        },
        visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: 'AWSManagedRulesAmazonIpReputationList',
        },
    },
    {
        name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet',
        priority: 30,
        statement: {
            managedRuleGroupStatement: {
                vendorName: 'AWS',
                name: 'AWSManagedRulesKnownBadInputsRuleSet',
            },
        },
        overrideAction: {
            none: {},
        },
        visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: 'AWSManagedRulesKnownBadInputsRuleSet',
        },
    },
];

// IP ranges allowed to access the API
export const InternalIPs = [
    '**********/19', // PAG
    '**********/16', // PAG
    '***********/19', // PAG
    '************/32', // MHP
];

// A construct to set up an internal developer firewall using AWS WAF
export class InternalDeveloperFirewallConstruct extends Construct {
    public constructor(
        scope: Construct,
        id: string,
        api: apigateway.RestApi,
        logGroup: logs.LogGroup,
        gatewayType: string,
        customRules?: CfnWebACL.RuleProperty[],

    ) {
        super(scope, id);

        // Create WAF ACL with rules
        const cfWebAcl = new waf.CfnWebACL(this, `${gatewayType}InternalDeveloperWaf`, {
            defaultAction: { allow: {} },
            visibilityConfig: {
                cloudWatchMetricsEnabled: true,
                metricName: 'waf',
                sampledRequestsEnabled: false,
            },
            description: 'Web Application Firewall',
            scope: 'REGIONAL',
            rules: customRules ?? BuildingBlockRules,
        });

        new waf.CfnLoggingConfiguration(this, 'LoggingConfig', {
            resourceArn: cfWebAcl.attrArn,
            logDestinationConfigs: [logGroup.logGroupArn],
        });

        // Associate the WAF ACL with an API Gateway
        const wafAssociation = new waf.CfnWebACLAssociation(this, `${gatewayType}-Waf-Api-Gw-Association`, {
            webAclArn: cfWebAcl.attrArn,
            resourceArn: Fn.join('', [
                'arn:aws:apigateway:',
                Stack.of(this).region,
                '::/restapis/',
                api.restApiId,
                '/stages/',
                api.deploymentStage.stageName,
            ]),
        });

        // Define dependency to ensure API Gateway is created before WAF ACL is associated
        wafAssociation.node.addDependency(api);
    }
}
