import * as cdk from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';
import * as kms from 'aws-cdk-lib/aws-kms';


export interface SecretsManagerStackProps extends cdk.StackProps {
    stage: string;
    usecase: string;
}

export class SecretsManagerStack extends cdk.Stack {
    public readonly secretArn: string;

    constructor(scope: Construct, id: string, props: SecretsManagerStackProps) {
        super(scope, id, props);

        const secretsManagerKmsKeyArnExportName = `${props.stage}-${props.usecase}-secretsmanager-cmk-key-arn`;
        const secretsManagerKmsKeyArn = cdk.Fn.importValue(secretsManagerKmsKeyArnExportName);
        const secretsManagerKey = kms.Key.fromKeyArn(this, 'secretsManagerKey', secretsManagerKmsKeyArn);

        //TODO: Value übergeben oder einmal selber ändern?
        const secret = new secretsmanager.Secret(this, 'WVDBClientSecret', {
            secretName: `${props.stage}-${props.usecase}-client-secret`,
            description: 'The client secret for ppn',
            encryptionKey: secretsManagerKey,
        });

        this.secretArn = secret.secretArn;

        new cdk.CfnOutput(this, 'WVDBClientSecretArnOutput', {
            value: this.secretArn,
            exportName: `${props.stage}-${props.usecase}-client-secret`,
        });
    }
}
