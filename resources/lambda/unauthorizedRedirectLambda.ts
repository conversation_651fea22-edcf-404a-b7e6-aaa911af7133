import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import LoggingHelper from './helper/loggingHelper';
import { v4 as uuidv4 } from 'uuid';

const cookieDomain = process.env.COOKIE_DOMAIN;

// Lambda function handler for unauthorized redirects
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    LoggingHelper.infoReq("unauthorizedRedirect", "handler", [event], `Unauthorized redirect request`);

    if (!cookieDomain) {
        LoggingHelper.error("unauthorizedRedirect", "handler", [], null, uuidv4(), 'Missing cookie domain');
        return {
            statusCode: 302,
            headers: {
                Location: '/auth', // Fallback to login
            },
            body: 'Missing configuration - redirecting to login',
        };
    }

    // Get the original path from query parameters or default to root
    const originalPath = event.queryStringParameters?.path ?
        decodeURIComponent(event.queryStringParameters.path) :
        '/';

    LoggingHelper.infoResp("unauthorizedRedirect", "handler", [originalPath], `Redirecting to refresh with original path: ${originalPath}`);

    // Store the original path in a cookie and redirect to refresh endpoint
    return {
        statusCode: 302,
        multiValueHeaders: {
            'Set-Cookie': [
                `OriginalPath=${encodeURIComponent(originalPath)}; Domain=.${cookieDomain}; Secure; SameSite=Lax; Max-Age=300`, // 5 minutes
            ],
            Location: [`https://${cookieDomain}/refresh-token`],
        },
        body: 'Redirecting to refresh token endpoint',
    };
};
