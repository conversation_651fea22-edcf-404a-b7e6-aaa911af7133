import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import LoggingHelper from './helper/loggingHelper';
import { v4 as uuidv4 } from 'uuid';

const cookieDomain = process.env.COOKIE_DOMAIN;

// Lambda function handler for unauthorized redirects
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    LoggingHelper.infoReq("unauthorizedRedirect", "handler", [event], `Unauthorized redirect request`);

    if (!cookieDomain) {
        LoggingHelper.error("unauthorizedRedirect", "handler", [], null, uuidv4(), 'Missing cookie domain');
        return {
            statusCode: 302,
            headers: {
                Location: '/auth', // Fallback to login
            },
            body: 'Missing configuration - redirecting to login',
        };
    }

    // Try to get the original path from various sources
    let originalPath = '/';

    // 1. Check query parameters first
    if (event.queryStringParameters?.path) {
        originalPath = decodeURIComponent(event.queryStringParameters.path);
    }
    // 2. Check if there's a stored path in cookies
    else if (event.headers?.cookie || event.headers?.Cookie) {
        const cookie = event.headers?.cookie ?? event.headers?.Cookie;
        const cookies = cookie.split(';');
        for (const cookieItem of cookies) {
            const [name, value] = cookieItem.split('=');
            if (name.trim() === 'OriginalPath') {
                originalPath = decodeURIComponent(value.trim());
                break;
            }
        }
    }
    // 3. Check referer header as fallback
    else if (event.headers?.referer || event.headers?.Referer) {
        const referer = event.headers?.referer || event.headers?.Referer;
        try {
            const url = new URL(referer);
            originalPath = url.pathname + url.search;
        } catch (error) {
            LoggingHelper.error("unauthorizedRedirect", "handler", [referer], error, uuidv4(), 'Failed to parse referer');
        }
    }

    LoggingHelper.infoResp("unauthorizedRedirect", "handler", [originalPath], `Redirecting to refresh with original path: ${originalPath}`);

    // Store the original path in a cookie and redirect to refresh endpoint
    return {
        statusCode: 302,
        multiValueHeaders: {
            'Set-Cookie': [
                `OriginalPath=${encodeURIComponent(originalPath)}; Domain=.${cookieDomain}; Secure; SameSite=Lax; Max-Age=300`, // 5 minutes
            ],
            Location: [`https://${cookieDomain}/refresh-token`],
        },
        body: 'Redirecting to refresh token endpoint',
    };
};
