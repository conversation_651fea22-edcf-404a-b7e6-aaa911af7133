import { APIGatewayProxyEvent } from 'aws-lambda/trigger/api-gateway-proxy';

exports.handler = async function (event: APIGatewayProxyEvent) {
    const start = Date.now();

    // Erlaube alle Anfragen
    const policyDocument = {
        Version: '2012-10-17',
        Statement: [
            {
                Action: 'execute-api:Invoke',
                Effect: 'Allow',
                Resource: event.resource, // Erlaubt den Zugriff auf die angeforderte API-Ressource
            },
        ],
    };

    // Die Antwort muss von der Lambda-Autorisierung die folgende Struktur haben
    const response = {
        principalId: 'anonymous', // Eindeutige ID für den Benutzer/Client, der die Anfrage stellt
        policyDocument, // Das IAM-Policy-Dokument, das den Zugriff steuert
        // Optional: Kontextdaten, die mit der Policy zurückgegeben werden können
        context: {
            message: 'access granted',
        },
    };

    return response;
};
