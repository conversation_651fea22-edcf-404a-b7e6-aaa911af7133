import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { stringify } from 'querystring';
import * as jwt from 'jsonwebtoken';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { AuthEndpointResponse } from './helper/types';
import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";
import LoggingHelper from './helper/loggingHelper';
import { v4 as uuidv4 } from 'uuid';
import { UserData } from './helper/types';

const stage = process.env.STAGE;
const ssmClient = new SSMClient({ region: "eu-west-1" });


// Lambda function handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    //const client = new SecretsManagerClient({ region: 'eu-west-1' });
    // Retrieve required environment variables
    const userPoolClientId = process.env.USER_POOL_CLIENT_ID;
    const redirectUri = process.env.REDIRECT_URI;
    const loginUrl = process.env.LOGIN_URL;
    const basePPNUrl = process.env.PPN_BASE_URL;
    if (!basePPNUrl) throw new Error('Base PPN Url not set.');

    const idpTokenUrl = process.env.TOKEN_URL;
    if (!idpTokenUrl) throw new Error('Idp Token url not set.');
    const cookieDomain = process.env.COOKIE_DOMAIN;

    const clientSecretParameter = process.env.CLIENT_SECRET_PARAMETER;
    if (!clientSecretParameter) throw new Error('Client secret Parameter is not set.');

    const clientSecret = await getSecureParameter(clientSecretParameter) ?? '';

    const errorDescription = event.queryStringParameters?.error_description;
    const error = event.queryStringParameters?.error;

    if (error && errorDescription === "Insufficient user permissions") {
        return {
            statusCode: 302,
            headers: {
                Location: basePPNUrl,
            },
            body: "Insufficient permissions to access application",
        };
    }

    const code = event.queryStringParameters?.code;

    // Check if the code is missing and return an error if it is
    if (!code) {
        return {
            statusCode: 302,
            headers: {
                Location: `${loginUrl}?client_id=${userPoolClientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
            },
            body: 'Missing query parameter: code',
        };
    }

    // Prepare the POST data for the token request
    const postData = stringify({
        grant_type: 'authorization_code',
        client_id: userPoolClientId,
        client_secret: clientSecret,
        code,
        redirect_uri: redirectUri,
    });

    const response = await fetch(idpTokenUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Content-Length': `${Buffer.byteLength(postData)}`,
        },
        body: postData,
    });
    if (!response.ok) {
        LoggingHelper.error("codeExchangeLambda", "fetchToken", [response.status, response.statusText, response.url, postData], null, uuidv4(), 'failed to fetch access token');
        return {
            statusCode: response.status,
            body: 'Auth Failure',
        };
    }
    const data = (await response.json()) as { access_token?: string; refresh_token?: string };
    LoggingHelper.infoResp("codeExchangeLambda", "getUserAttributes", [data], `response data`);

    const accessToken = data.access_token;
    const refreshToken = data.refresh_token;

    if (!accessToken) {
        LoggingHelper.error("codeExchangeLambda", "fetchToken", [response.status, response.statusText, response.url, data], null, uuidv4(), 'failed to fetch access token');
        return {
            statusCode: response.status,
            body: 'Auth Failure',
        };
    }

    if (!refreshToken) {
        LoggingHelper.error("codeExchangeLambda", "fetchToken", [response.status, response.statusText, response.url, data], null, uuidv4(), 'failed to fetch refresh token');
        return {
            statusCode: response.status,
            body: 'Auth Failure - Missing refresh token',
        };
    }

    const decodedToken = jwt.decode(accessToken) as jwt.JwtPayload | null;
    const now = Math.floor(Date.now() / 1000);
    const tokenExp = decodedToken?.exp ?? Math.floor(Date.now() / 1000) + 900; //<-- Default is 15 minutes
    const maxAge = tokenExp - now;
    const username = decodedToken?.sub ?? '';
    const userAttributes = await getUserAttributes(accessToken, idpTokenUrl, username);

    return {
        statusCode: userAttributes.statuscode === 200 ? 302 : userAttributes.statuscode,
        body:
            userAttributes.statuscode === 200
                ? JSON.stringify({ message: `Access token set in Authorization cookie` })
                : userAttributes.data,
        multiValueHeaders: {
            'Set-Cookie': [
                `Authorization=${accessToken}; Domain=.${cookieDomain}; HttpOnly; Secure; SameSite=Lax;Max-Age=${maxAge}`,
                `RefreshToken=${refreshToken}; Domain=.${cookieDomain}; HttpOnly; Secure; SameSite=Lax;Max-Age=${maxAge}`,
                `UserAttributes=${userAttributes.data}; Domain=.${cookieDomain}; Secure; SameSite=Lax;Max-Age=${maxAge}`,
                `SessionExp=${tokenExp}; Domain=.${cookieDomain}; Secure; SameSite=Lax;Max-Age=${maxAge}`,
            ],
            Location: ['/'],
        },
    };
};

async function getUserAttributes(token: string, idpTokenUrl: string, username: string): Promise<{ statuscode: number; data: string }> {

    const u2aClientId = process.env.U2A_CLIENT_ID;
    if (!u2aClientId) throw new Error('U2A client id not set.');

    const masterDataUrl = process.env.MASTER_DATA_URL;
    if (!masterDataUrl) throw new Error('Masterdata url not set.');

    const masterDataClientId = process.env.MASTER_DATA_CLIENT_ID;
    if (!masterDataClientId) throw new Error('Masterdata client id not set.');

    const masterDataClientSecretParameter = process.env.MASTER_DATA_CLIENT_SECRET_PARAMETER;
    if (!masterDataClientSecretParameter) throw new Error('Masterdata client secret not set.');

    const importerRoleCode = process.env.IMPORTER_ROLE_CODE;
    if (!importerRoleCode) throw new Error('Importer Role Code not set.');

    const masterDataClientSecret = await getSecureParameter(masterDataClientSecretParameter) ?? '';

    const u2aClientSecretParameter = process.env.U2A_CLIENT_SECRET_PARAMETER;
    if (!u2aClientSecretParameter) throw new Error('U2A Client secret Parameter is not set.');

    const u2aClientSecret = await getSecureParameter(u2aClientSecretParameter) ?? '';



    const u2aAccessToken = await getU2AAccessToken(token, u2aClientId, u2aClientSecret, idpTokenUrl);
    if (!u2aAccessToken) {
        LoggingHelper.error("codeExchangeLambda", "fetchU2aToken", [], null, uuidv4(), 'failed to fetch u2a access token');
        return {
            statuscode: 400,
            data: 'Auth Failure',
        };
    }

    const baseUrlUser = `${masterDataUrl}/users/${username}`;
    const queryParams = stringify({
        'fields[user]': 'applicationRoles',
        'include': 'applicationRoles'
    });

    const userUrl = `${baseUrlUser}?${queryParams}`;

    const userResponse = await fetch(userUrl, {
        method: 'GET',
        headers: {
            'X-Porsche-Client-Id': masterDataClientId,
            'X-Porsche-Client-Secret': masterDataClientSecret,
            'Authorization': `Bearer ${u2aAccessToken}`,
        },
    });

    const ppnUserData = await userResponse.json();
    LoggingHelper.infoResp("codeExchangeLambda", "ppnUserResponse", [ppnUserData], `response data`);
    const userId = ppnUserData.data.id;
    const publicGroupCodes = ppnUserData?.included?.map((item: any) => item?.attributes?.publicGroupCode) || [];
    const isImporter = publicGroupCodes.includes(importerRoleCode);
    let importerCode;
    const organizationId = ppnUserData?.data?.relationships?.importer?.data?.id;
    if (organizationId) {
        const orgUrl = `${masterDataUrl}/organizations/${organizationId}`;

        const orgResponse = await fetch(orgUrl, {
            method: 'GET',
            headers: {
                'X-Porsche-Client-Id': masterDataClientId,
                'X-Porsche-Client-Secret': masterDataClientSecret,
                'Authorization': `Bearer ${u2aAccessToken}`,
            },
        });

        const ppnOrgData = await orgResponse.json();
        LoggingHelper.infoResp("codeExchangeLambda", "ppnOrgResponse", [ppnOrgData], `response data`);

        importerCode = ppnOrgData?.data?.attributes?.importerCode ?? 'UNDEFINED';
    } else {
        importerCode = 'UNDEFINED'
    }

    const user: UserData = {
        id: userId,
        isImporter: isImporter,
        username: username,
        importerCode: importerCode
    };
    return { statuscode: 200, data: Buffer.from(JSON.stringify(user)).toString('base64') };

}

async function getSecureParameter(parameterName: string): Promise<string | undefined> {
    try {
        const command = new GetParameterCommand({
            Name: parameterName,
            WithDecryption: true,
        });

        const response = await ssmClient.send(command);
        return response.Parameter?.Value;
    } catch (error) {
        console.error("Fehler beim Abrufen des Parameters:", error);
        return undefined;
    }
}

async function getU2AAccessToken(token: string, u2aClientId: string, u2aClientSecret: string, idpTokenUrl: string): Promise<string | undefined> {
    const postData = stringify({
        grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
        ppn_resource: 'urn:ppn:ppn-masterdata-api',
        subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
        client_id: u2aClientId,
        client_secret: u2aClientSecret,
        subject_token: token,
    });

    const response = await fetch(idpTokenUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Content-Length': `${Buffer.byteLength(postData)}`,
        },
        body: postData,
    });

    if (!response.ok) {
        LoggingHelper.error("codeExchangeLambda", "fetchU2AToken", [response.status, response.statusText, response.url, postData], null, uuidv4(), 'failed to fetch u2a access token');
    }
    const data = (await response.json()) as { access_token?: string };
    return data.access_token;
}
