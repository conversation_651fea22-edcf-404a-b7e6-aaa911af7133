import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { stringify } from 'querystring';
import * as jwt from 'jsonwebtoken';
import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";
import LoggingHelper from './helper/loggingHelper';
import { v4 as uuidv4 } from 'uuid';

const ssmClient = new SSMClient({ region: "eu-west-1" });

const tokenUrl = process.env.TOKEN_URL;
const clientId = process.env.CLIENT_ID;
const clientSecretParameter = process.env.CLIENT_SECRET_PARAMETER;
const cookieDomain = process.env.COOKIE_DOMAIN;
const loginUrl = process.env.LOGIN_URL;
const redirectUri = process.env.REDIRECT_URI;

// Get secure parameter from AWS Systems Manager Parameter Store
async function getSecureParameter(parameterName: string): Promise<string | undefined> {
    try {
        const command = new GetParameterCommand({
            Name: parameterName,
            WithDecryption: true,
        });

        const response = await ssmClient.send(command);
        return response.Parameter?.Value;
    } catch (error) {
        LoggingHelper.error("refreshToken", "getSecureParameter", [parameterName], error, uuidv4(), 'Failed to get secure parameter');
        return undefined;
    }
}

// Extract the 'RefreshToken' cookie value from the cookieHeader string
const getRefreshTokenFromCookie = (cookieHeader: string | undefined): string | undefined => {
    if (!cookieHeader) {
        return undefined;
    }

    const refreshTokenCookieName = 'RefreshToken';
    const cookies = cookieHeader.split(';');

    for (const cookie of cookies) {
        const [name, value] = cookie.split('=');
        if (name.trim() === refreshTokenCookieName) {
            return value.trim();
        }
    }

    return undefined;
};

// Extract the 'UserAttributes' cookie value from the cookieHeader string
const getUserAttributesFromCookie = (cookieHeader: string | undefined): string | undefined => {
    if (!cookieHeader) {
        return undefined;
    }

    const userAttributesCookieName = 'UserAttributes';
    const cookies = cookieHeader.split(';');

    for (const cookie of cookies) {
        const [name, value] = cookie.split('=');
        if (name.trim() === userAttributesCookieName) {
            return value.trim();
        }
    }

    return undefined;
};

// Lambda function handler for token refresh
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    LoggingHelper.infoReq("refreshToken", "handler", [], `Token refresh request`);

    if (!tokenUrl || !clientSecretParameter || !cookieDomain) {
        LoggingHelper.error("refreshToken", "handler", [], null, uuidv4(), 'Missing required environment variables');
        return {
            statusCode: 302,
            headers: {
                Location: `${loginUrl}?client_id=${clientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
            },
            body: 'Server configuration error - redirecting to login',
        };
    }

    // Get refresh token and user attributes from cookies
    const cookie = event.headers?.cookie ?? event.headers?.Cookie;
    const refreshToken = getRefreshTokenFromCookie(cookie);
    const userAttributes = getUserAttributesFromCookie(cookie);

    if (!refreshToken) {
        LoggingHelper.error("refreshToken", "handler", [], null, uuidv4(), 'Missing refresh token - redirecting to login');
        return {
            statusCode: 302,
            headers: {
                Location: `${loginUrl}?client_id=${clientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
            },
            body: 'Missing refresh token - redirecting to login',
        };
    }

    if (!userAttributes) {
        LoggingHelper.error("refreshToken", "handler", [], null, uuidv4(), 'Missing user attributes - redirecting to login');
        return {
            statusCode: 302,
            headers: {
                Location: `${loginUrl}?client_id=${clientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
            },
            body: 'Missing user attributes - redirecting to login',
        };
    }

    try {
        const clientSecret = await getSecureParameter(clientSecretParameter);
        if (!clientSecret) {
            LoggingHelper.error("refreshToken", "handler", [], null, uuidv4(), 'Failed to get client secret');
            return {
                statusCode: 302,
                headers: {
                    Location: `${loginUrl}?client_id=${clientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
                },
                body: 'Server configuration error - redirecting to login',
            };
        }

        const postData = stringify({
            grant_type: 'refresh_token',
            refresh_token: refreshToken,
        });

        const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

        const response = await fetch(tokenUrl, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Authorization': `Basic ${authHeader}`,
                'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
                'Content-Length': `${Buffer.byteLength(postData)}`,
            },
            body: postData,
        });

        if (!response.ok) {
            LoggingHelper.error("refreshToken", "handler", [response.status, response.statusText], null, uuidv4(), 'Token refresh failed - redirecting to login');
            return {
                statusCode: 302,
                headers: {
                    Location: `${loginUrl}?client_id=${clientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
                },
                body: 'Token refresh failed - redirecting to login',
            };
        }

        const data = await response.json() as { access_token?: string; refresh_token?: string; expires_in?: number };

        if (!data.access_token || !data.refresh_token) {
            LoggingHelper.error("refreshToken", "handler", [data], null, uuidv4(), 'Invalid token response - redirecting to login');
            return {
                statusCode: 302,
                headers: {
                    Location: `${loginUrl}?client_id=${clientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
                },
                body: 'Invalid token response - redirecting to login',
            };
        }

        // Calculate expiration
        const now = Math.floor(Date.now() / 1000);
        const expiresIn = data.expires_in || 900; // Default 15 minutes
        const tokenExp = now + expiresIn;
        const maxAge = expiresIn;

        LoggingHelper.infoResp("refreshToken", "handler", [data], 'Token refresh successful - redirecting back');

        // Get the original URL from query parameters or default to root
        const originalUrl = event.queryStringParameters?.redirect || '/';

        return {
            statusCode: 302,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
                'Access-Control-Allow-Methods': 'GET,OPTIONS',
            },
            multiValueHeaders: {
                'Set-Cookie': [
                    `Authorization=${data.access_token}; Domain=.${cookieDomain}; HttpOnly; Secure; SameSite=Lax;Max-Age=${maxAge}`,
                    `RefreshToken=${data.refresh_token}; Domain=.${cookieDomain}; HttpOnly; Secure; SameSite=Lax;Max-Age=${maxAge}`,
                    `UserAttributes=${userAttributes}; Domain=.${cookieDomain}; Secure; SameSite=Lax;Max-Age=${maxAge}`,
                    `SessionExp=${tokenExp}; Domain=.${cookieDomain}; Secure; SameSite=Lax;Max-Age=${maxAge}`,
                ],
                Location: [originalUrl], // Redirect back to original URL
            },
            body: 'Token refreshed successfully - redirecting back',
        };

    } catch (error) {
        LoggingHelper.error("refreshToken", "handler", [], error, uuidv4(), 'Token refresh error - redirecting to login');
        return {
            statusCode: 302,
            headers: {
                Location: `${loginUrl}?client_id=${clientId}&response_type=code&scope=openid&redirect_uri=${redirectUri}`,
            },
            body: 'Token refresh error - redirecting to login',
        };
    }
};
