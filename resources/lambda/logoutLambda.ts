import { APIGatewayProxyResult } from 'aws-lambda';

const stage = process.env.STAGE;

export const handler = async (): Promise<APIGatewayProxyResult> => {
    const redirectURL = process.env.REDIRECT_URL;
    const cookieDomain = process.env.COOKIE_DOMAIN;

    return {
        statusCode: 302,
        multiValueHeaders: {
            Location: [`${redirectURL}`],
            'Set-Cookie': [
                `Authorization-${stage}=deleted; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`,
                `UserAttributes-${stage}=deleted; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`,
                `SessionExp-${stage}=deleted; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`,
            ],
        },
        body: 'Logout successful',
    };
};
