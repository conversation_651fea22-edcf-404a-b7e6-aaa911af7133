class LoggingHelper {
    static infoReq(initiator: any, action: string, args: any[], ...additionalInfos: any[]) {
        this.logReq(initiator, action, args, additionalInfos);
    }

    private static logReq(initiator: any, action: string, args: any[], additionalInfos: any[]) {
        const entry = this.getReqLogEntry(initiator, action, args, additionalInfos);
        console.info(entry);
    }

    static infoResp(initiator: any, action: string, returnValue: any, ...additionalInfos: any[]) {
        this.logResp(initiator, action, returnValue, additionalInfos);
    }

    private static logResp(initiator: any, action: string, returnValue: any, additionalInfos: any[]) {
        const entry = this.getRespLogEntry(initiator, action, returnValue, additionalInfos);
        console.info(entry);
    }

    static error(initiator: any, action: string, args: any[], e: unknown, uuid: string, ...additionalInfos: any[]) {
        this.logError(initiator, action, args, e, uuid, additionalInfos);
    }

    private static logError(initiator: any, action: string, args: any[], e: unknown, uuid: string, additionalInfos: any[]) {
        const error = this.convertToError(e);

        let entry = this.getErrorLogEntry(initiator, action, args, error, uuid, additionalInfos);
        const exception = this.buildExceptionEntry(error);
        entry = entry.replace(/}$/, `, "exception": ${exception}}`);
        console.error(entry);
    }

    private static convertToError(e: unknown): Error {
        if (e instanceof Error) {
            return e;
        }
        return new Error(`Unknown error: ${String(e)}`);
    }

    private static buildExceptionEntry(t: Error | null): string {
        const exceptionMap: Record<string, any> = {
            cause: (t as any)?.cause?.name ?? t?.name ?? '',
            message: t?.message ? this.jsonEscape(t.message) : t?.name ?? '',
        };
        const stackTrace = this.getStackTrace(t);
        exceptionMap.stacktrace = stackTrace.split('\n');
        return JSON.stringify(exceptionMap);
    }

    private static getStackTrace(throwable: Error | null): string {
        return this.jsonEscape(throwable?.stack ?? '');
    }

    private static jsonEscape(string: string): string {
        return string.replace(/"/g, "'").replace(/\r/g, '').replace(/\t/g, '');
    }

    private static getReqLogEntry(initiator: any, action: string, args: any[], ...additionalInfos: any[]): string {
        return JSON.stringify({ initiator, action, args, additionalInfos: additionalInfos?.flat() });
    }

    private static getRespLogEntry(initiator: any, action: string, returnValue: any, ...additionalInfos: any[]): string {
        return JSON.stringify({ initiator, action, returnValue, additionalInfos: additionalInfos?.flat() });
    }

    private static getErrorLogEntry(initiator: any, action: string, args: any[], e: Error | null, uuid: string, ...additionalInfos: any[]): string {
        return JSON.stringify({ initiator, action, args, error: e?.message, uuid, additionalInfos });
    }
}

export default LoggingHelper;
