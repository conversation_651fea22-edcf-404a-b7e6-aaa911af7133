export interface AuthEndpointResponse {
    username: string; //'xyzxyz';
    firstName: string; //'Max';
    lastName: string; //'Mustermann';
    porschePartnerNo: string; //'9500090';
    applications: Record<
        string, //'tyd'
        Application[]
    >;
}
export interface Application {
    role: string; //'power_master_user'
    permissions: string[]; //['read', 'write']
    modelTypeVisibility: string; //'DLR'
}

export interface UserData {
    id: string,
    isImporter: boolean;
    username: string;
    importerCode: string;
}