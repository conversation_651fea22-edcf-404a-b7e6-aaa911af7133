{"name": "lambda", "version": "1.0.0", "scripts": {"build": "npm ci", "install_and_build": "npm ci && tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "types": "./index.d.ts", "description": "", "dependencies": {"@types/jsonwebtoken": "9.0.8", "aws-cdk-lib": "2.171.0", "aws-lambda": "^1.0.7", "axios": "^1.7.9", "jwks-rsa": "3.1.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@aws-sdk/client-secrets-manager": "^3.734.0", "@aws-sdk/client-ssm": "^3.734.0", "@types/aws-lambda": "^8.10.146", "uuid": "^11.0.3"}}