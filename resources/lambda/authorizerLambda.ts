import { APIGatewayRequestAuthorizerEvent, APIGatewayAuthorizerResult } from 'aws-lambda';
import * as jwt from 'jsonwebtoken';
import { JwksClient } from 'jwks-rsa';
import { AuthEndpointResponse, UserData } from './helper/types';
import LoggingHelper from './helper/loggingHelper';
import { v4 as uuidv4 } from 'uuid';

const publicKeyEndpoint = process.env.PUBLIC_KEY_ENDPOINT;
if (!publicKeyEndpoint) throw new Error('Public Key Endpoint not set.');

const issuer = process.env.ISSUER;
const clientId = process.env.CLIENT_ID;
const stage = process.env.STAGE;

let publicKeyCache: string | undefined = undefined;

// Create a new JWKS (JSON Web Key Set) client instance with the public key endpoint

const client = new JwksClient({
    jwksUri: publicKeyEndpoint,
});

// Asynchronously fetch the public key for a given Key ID (kid)
const fetchPublicKey = async (kid: string): Promise<string> => {
    const key = await client.getSigningKey(kid);
    return key.getPublicKey();
};

// Asynchronously get the public key for a given Key ID (kid) and cache it for future use
const getPublicKey = async (kid: string): Promise<string> => {
    if (!publicKeyCache) {
        publicKeyCache = await fetchPublicKey(kid);
    }
    return publicKeyCache;
};

// Asynchronously validate a JWT token using the public key, issuer
const validateToken = async (token: string): Promise<boolean> => {
    const options = {
        issuer,
    };

    // Decode the JWT token to access the header
    const decodedToken = jwt.decode(token, { complete: true });

    // Check if the decodedToken is valid and has a header with a kid property
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!decodedToken?.header?.kid) {
        LoggingHelper.infoResp("authorizer", "validateToken", decodedToken, 'Invalid token or missing kid in header');
        return false;
    }
    const kid = decodedToken.header.kid;

    const payload = decodedToken.payload as jwt.JwtPayload;
    if (payload.client_id !== clientId) {
        LoggingHelper.infoResp("authorizer", "validateToken", payload, `Invalid client_id in payload`);
        return false;
    }

    try {
        const publicKey = await getPublicKey(kid);
        LoggingHelper.infoReq("authorizer", "validateToken", [publicKey], "Validating token from publicKey");
        jwt.verify(token, publicKey, options);
        return true;
    } catch (error: unknown) {
        if (error instanceof Error) {
            LoggingHelper.error("authorizer", "validateToken", [error.message], error, uuidv4(), 'Token validation failed');
        } else {
            LoggingHelper.error("authorizer", "validateToken", [], null, uuidv4(), 'Token validation failed: Unknown error');
        }

        // Refresh the public key and retry validation
        publicKeyCache = await fetchPublicKey(kid);
        try {
            jwt.verify(token, publicKeyCache, options);
            return true;
        } catch (innerError: unknown) {
            if (innerError instanceof Error) {
                LoggingHelper.error("authorizer", "validateToken", [innerError.message], error, uuidv4(), 'Token validation failed');
            } else {
                LoggingHelper.error("authorizer", "validateToken", [], null, uuidv4(), 'Token validation failed: Unknown error');
            }
            return false;
        }
    }
};

// Generate an AWS IAM policy with the specified principalId, effect, and resource

const generateDenyPolicy = (
    principalId: string,
    resourcePath: string,
    errorMessage?: string,
): APIGatewayAuthorizerResult => {
    return {
        principalId,
        policyDocument: {
            Version: '2012-10-17',
            Statement: [
                {
                    Action: 'execute-api:Invoke',
                    Effect: 'Deny',
                    Resource: `${resourcePath}*`,
                },
            ],
        },
        context: {
            errorMessage: errorMessage,
        },
    };
};
const generateAllowPolicy = (principalId: string, resourcePath: string, importerCode: string, username: string): APIGatewayAuthorizerResult => {
    //read only access
    return {
        principalId,
        policyDocument: {
            Version: '2012-10-17',
            Statement: [
                {
                    Action: 'execute-api:Invoke',
                    Effect: 'Allow',
                    Resource: `${resourcePath}*`,
                },
            ],
        },
        context: {
            importerCode: importerCode,
            username: username,
        },
    };
};
// Extract the 'Authorization' cookie value from the cookieHeader string
const getAuthorizationFromCookie = (cookieHeader: string | undefined): string | undefined => {
    if (!cookieHeader) {
        return undefined;
    }

    const authCookieName = 'Authorization';

    // Split the cookieHeader into separate cookies and search for 'Authorization' cookie
    const cookies = cookieHeader.split(';');
    for (const cookie of cookies) {
        const [name, value] = cookie.split('=');
        if (name.trim() === authCookieName) {
            return value.trim();
        }
    }

    // Return undefined if the 'Authorization' cookie is not found
    return undefined;
};

function checkIfUserHasAccess(user: UserData, httpMethod: string, resourcePathWithMethod: string): boolean {
    LoggingHelper.infoReq("authorizer", "checkUserAccess", [user, httpMethod, resourcePathWithMethod], "UserAccess");

    if (user.isImporter || httpMethod === 'GET') {
        return true;
    }
    if (!user.isImporter && httpMethod === 'POST' && resourcePathWithMethod.includes('fetchResellers')) {
        return true;
    }


    return false;

}

const getUserAttributesFromCookie = (cookieHeader: string | undefined): UserData | undefined => {
    if (!cookieHeader) return undefined;

    const userAttributesCookieName = `UserAttributes`;
    const cookies = cookieHeader.split(';');

    for (const cookie of cookies) {
        const [name, value] = cookie.split('=');
        if (name.trim() === userAttributesCookieName) {
            try {
                const decodedValue = atob(value.trim());
                const userAttributes = JSON.parse(decodedValue);

                const userData: UserData = {
                    id: userAttributes.id || 'UNDEFINED',
                    isImporter: userAttributes.isImporter || false,
                    username: userAttributes.username || 'UNDEFINED',
                    importerCode: userAttributes.importerCode || 'UNDEFINED'
                };

                return userData;
            } catch (error) {
                console.error("Error parsing user attributes:", error);
                return undefined;
            }
        }
    }
    return undefined;
};

// Lambda function handler for AWS API Gateway custom authorizer
export const handler = async (event: APIGatewayRequestAuthorizerEvent): Promise<APIGatewayAuthorizerResult> => {
    LoggingHelper.infoReq("authorizer", "handler", [], `Event: ${JSON.stringify(event, null, 2)}`);



    // Get the 'cookie' header from the event
    const cookie = event.headers?.cookie ?? event.headers?.Cookie;
    const userAttributes = getUserAttributesFromCookie(cookie);
    const principalId = userAttributes?.id ?? 'unknown-user';
    // Get the authorizationHeader based on the event type and the 'cookie' header
    const authorizationHeader = getAuthorizationFromCookie(cookie);

    // Parse the event to get the resource path for the whole API
    const httpMethod = event.requestContext.httpMethod;
    const resourcePathWithoutMethod = `${event.methodArn.split(httpMethod)[0]}`;
    const resourcePathWithMethod = event.methodArn.split(httpMethod)[1];

    //return generateAllowPolicy(principalId, resourcePathWithoutMethod);
    // If there is no authorizationHeader, generate a deny policy
    if (!authorizationHeader) {
        LoggingHelper.error("authorizer", "handler", [], null, uuidv4(), 'Missing authorization header');
        throw new Error('Unauthorized');
    }

    // If the authorizationHeader is present, validate the token
    const isValid = await validateToken(authorizationHeader);

    //if token is not valid, redirect to refresh endpoint (which will try refresh or fallback to login)
    if (!isValid) {
        LoggingHelper.error("authorizer", "handler", [], null, uuidv4(), 'Invalid token - redirecting to refresh endpoint');
        throw new Error('Unauthorized'); // This triggers redirect to refresh-token endpoint
    }

    try {
        // If the token is valid, check if the user is in the access group
        //const userDataFromAuthEndpoint = await getAuthUserData(authorizationHeader);
        if (!userAttributes) {
            LoggingHelper.error("authorizer", "handler", [], null, uuidv4(), 'Missing user data');
            throw new Error('Unauthorized');
        }
        const userHasAccess = checkIfUserHasAccess(userAttributes, httpMethod, resourcePathWithMethod);

        // If user is in access group, generate an allow policy; otherwise, generate a deny policy
        if (!userHasAccess) {
            return generateDenyPolicy(principalId, resourcePathWithoutMethod, 'User does not have importer role');
        } else {
            const policy = generateAllowPolicy(principalId, resourcePathWithoutMethod, userAttributes.importerCode, userAttributes.username);
            return policy;
        }
    } catch (error) {
        if (error instanceof Error) {
            return generateDenyPolicy(principalId, resourcePathWithoutMethod, error.message);
        }
    }

    return generateDenyPolicy(principalId, resourcePathWithoutMethod);
};
