# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.1.3](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/compare/v0.1.2...v0.1.3) (2025-02-17)

### [0.1.2](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/compare/v0.1.1...v0.1.2) (2025-02-17)

### 0.1.1 (2025-02-17)


### Features

* **iac:** Adjusted lambdas, gateway and dynamoDB ([1ace2fe](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/1ace2fe8102183655f359f49cd0a11d0080ebb2a))
* **iac:** Fix security findings ([03252c1](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/03252c125e91d9b2c07e65c3bb9e3451edbc2b77))
* **iac:** WVDB-10 Implement ppn master data calls and user authorization ([63b3148](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/63b31484a1dc921d031b613d22a3ff110adb5617))
* **iac:** WVDB-15 Added Cloudfront ([ac2b7a4](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/ac2b7a46c0908762370a3a604ef056d12ba08012))
* **iac:** WVDB-17 Added FrontendStacks ([bd54651](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/bd54651f4c0a019edb079278f2fc067dc4af908e))
* **iac:** WVDB-19 Added initial dynamodb, vpc and kms stack + gitlab pipeline ([bb38dda](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/bb38dda567e96ca943f9ef15c2994d73cfbcda63))
* **iac:** WVDB-21 Adjusted pipeline ([737ae20](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/737ae208bfe1a06e4154d2154e81e6af3e386913))


### Bug Fixes

* **iac:** Asjust package to analyse for cfn guard ([dbfa89c](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/dbfa89c8d1c4d59f8fc554b3ee502b20d27b2c21))
* **iac:** Fix CFN Guard ([e579bb6](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/e579bb61ec14d7fb61fb8551923d941c50b17df1))
* **iac:** Fix typo in pipeline ([cfd3415](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/cfd34157902be1cb23e4bf1f86663d7fc62772df))
* **iac:** Fixed sonarqube scan issue ([32fe284](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/32fe2845404d719c78560acc1d93e21770cae4a9))
* **iac:** WVDB-15 Fix duplicate stack name ([d61313d](https://cicd.skyway.porsche.com/WVDB/wvdb-infrastructure/commit/d61313d5dee1557989bfd84cbd7e74fc2c8492ce))
