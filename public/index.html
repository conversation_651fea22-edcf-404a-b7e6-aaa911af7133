<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Overview</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
      }

      nav {
        background-color: #333;
        overflow: hidden;
      }

      nav a {
        float: left;
        display: block;
        color: white;
        text-align: center;
        padding: 14px 16px;
        text-decoration: none;
      }

      nav a:hover {
        background-color: #ddd;
        color: black;
      }

      nav a.active {
        background-color: #ff0000; /* Rot als Highlight-Farbe */
        color: white !important; /* !important, um sicherzustellen, dass es Vorrang hat */
      }

      iframe {
        width: 100%;
        height: 80vh;
        border: none;
      }
    </style>
  </head>
  <body>
    <nav>
      <a href="javascript:void(0);" onclick="loadContent('dependency')">Dependency Check</a>
    </nav>

    <iframe id="contentFrame" src="dependencies.html" frameborder="0"></iframe>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // Initial Live Demo link highlight
        document.querySelector('a[href="javascript:void(0);"]').classList.add('active');
      });

      function loadContent(contentType) {
        var iframe = document.getElementById('contentFrame');
        var links = document.getElementsByTagName('a');

        for (var i = 0; i < links.length; i++) {
          links[i].classList.remove('active');
        }

        if (contentType === 'dependency') {
          iframe.src = 'dependencies.html';
        }

        // Highlight the selected link
        var selectedLink = document.querySelector('a[href="javascript:void(0);"][onclick="loadContent(\'' + contentType + '\')"]');
        if (selectedLink) {
          selectedLink.classList.add('active');
        }
      }
    </script>
  </body>
</html>
