AWSTemplateFormatVersion: '2010-09-09'
Description: Cloudformation template to create dynamodb tables of wvdb
Parameters:
  Stage:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    AllowedValues:
      - dev
      - int
      - prod
    Default: dev
  UseCase:
    Description: 'Usecase'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z\-]*$'
    Default: wvdb
  PrimaryRegion:
    Description: 'Primary region for the resources'
    Type: String
    Default: eu-west-1
  ReplicaRegion:
    Description: 'Replication region for the resources'
    Type: String
    Default: eu-central-1
  ReplicaKmsId:
    Description: 'KMS Id for the replication region'
    Type: String
    Default: '' # Empty string as default value to mark this parameter as optional
  ReplicationNeeded:
    Description: 'Flag to control if replication is needed'
    Type: String
    AllowedValues:
      - true
      - false
    Default: false

Conditions:
  IsReplicationNeeded: !Equals [!Ref ReplicationNeeded, true]

Resources:
  wvdbTable:
    Type: AWS::DynamoDB::GlobalTable
    Properties:
      TableName: !Sub '${Stage}-${UseCase}'
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: S
        - AttributeName: entityType
          AttributeType: S          
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: GSI_ENTITY_TYPE
          KeySchema:
            - AttributeName: entityType
              KeyType: HASH
            - AttributeName: pk
              KeyType: RANGE
          Projection:
            ProjectionType: ALL          
      SSESpecification:
        SSEEnabled: true
        SSEType: KMS
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: 'KEYS_ONLY'
      Replicas:
        - Region: !Ref PrimaryRegion
          SSESpecification:
            KMSMasterKeyId: { 'Fn::ImportValue': !Sub '${Stage}-${UseCase}-dynamodb-cmk-key-arn' }
          PointInTimeRecoverySpecification:
            PointInTimeRecoveryEnabled: true
        - !If
          - IsReplicationNeeded
          - Region: !Ref ReplicaRegion
            SSESpecification:
              KMSMasterKeyId: !Sub '${ReplicaKmsId}'
            PointInTimeRecoverySpecification:
              PointInTimeRecoveryEnabled: true
          - !Ref 'AWS::NoValue'
    UpdateReplacePolicy: Retain
    DeletionPolicy: Retain
  wvdbDeleteLogTable:
    Type: AWS::DynamoDB::GlobalTable
    Properties:
      TableName: !Sub '${Stage}-${UseCase}-deletion-log'
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: S
        - AttributeName: entityType
          AttributeType: S          
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: GSI_ENTITY_TYPE
          KeySchema:
            - AttributeName: entityType
              KeyType: HASH
            - AttributeName: pk
              KeyType: RANGE
          Projection:
            ProjectionType: ALL          
      SSESpecification:
        SSEEnabled: true
        SSEType: KMS
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: 'KEYS_ONLY'
      Replicas:
        - Region: !Ref PrimaryRegion
          SSESpecification:
            KMSMasterKeyId: { 'Fn::ImportValue': !Sub '${Stage}-${UseCase}-dynamodb-cmk-key-arn' }
          PointInTimeRecoverySpecification:
            PointInTimeRecoveryEnabled: true
        - !If
          - IsReplicationNeeded
          - Region: !Ref ReplicaRegion
            SSESpecification:
              KMSMasterKeyId: !Sub '${ReplicaKmsId}'
            PointInTimeRecoverySpecification:
              PointInTimeRecoveryEnabled: true
          - !Ref 'AWS::NoValue'
    UpdateReplacePolicy: Retain
    DeletionPolicy: Retain