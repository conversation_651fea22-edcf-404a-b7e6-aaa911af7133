AWSTemplateFormatVersion: '2010-09-09'
Description: Cloudformation template to create eventbridge of wvdb
Parameters:
  Stage:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    AllowedValues:
      - dev
      - prod
    Default: dev
  UseCase:
    Description: 'Use Case'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z\-]*$'
    Default: wvdb

Resources:
  ExpirationEventBus:
    Type: AWS::Events::EventBus
    Properties:
      Name: !Sub '${Stage}-${UseCase}-expiration-event-bus'
  ExpirationArchive:
    Type: AWS::Events::Archive
    DependsOn: ExpirationEventBus
    Properties:
      ArchiveName: !Sub '${Stage}-${UseCase}-expiration-event-archive'
      Description: Archive for expiration event bus
      RetentionDays: 30
      SourceArn: !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/${Stage}-${UseCase}-expiration-event-bus'
  DefaultArchive:
    Type: 'AWS::Events::Archive'
    Properties:
      ArchiveName: !Sub '${Stage}-${UseCase}-default-event-archive'
      Description: Archive for default event bus
      RetentionDays: 30
      SourceArn: !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/default'      

  ExpirationEventsForwardingRole:
    Type: AWS::IAM::Role
    DependsOn: ExpirationEventBus
    Properties:
      RoleName: !Sub '${Stage}-${UseCase}-${AWS::Region}-expiration-event-forwarding-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - events.amazonaws.com
            Action: sts:AssumeRole
  ExpirationEventsForwardingPolicy:
    Type: AWS::IAM::ManagedPolicy
    DependsOn: ExpirationEventsForwardingRole
    Properties:
      Roles:
        - !Ref ExpirationEventsForwardingRole
      ManagedPolicyName: !Sub '${Stage}-${UseCase}-${AWS::Region}-event-forwarding-policy'
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - events:PutEvents
            Resource:
              - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/${Stage}-${UseCase}-expiration-event-bus'

  SSMExpirationEventsForwardingRule:
    Type: AWS::Events::Rule
    DependsOn: ExpirationEventsForwardingPolicy
    Properties:
      Description: Rule for forwarding expiration notification to custom event bus
      EventBusName: default
      State: ENABLED
      EventPattern:
        source:
          - aws.ssm
        detail-type:
          - Parameter Store Policy Action
      Targets:
        - Arn: !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/${Stage}-${UseCase}-expiration-event-bus'
          Id: !Sub '${Stage}-${UseCase}-ExpirationForwarding'
          RoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${Stage}-${UseCase}-expiration-event-forwarding-role'
  SSMExpirationRule:
    Type: AWS::Events::Rule
    Properties:
      Description: Rule for expiration notification via sns
      EventBusName: !Sub '${Stage}-${UseCase}-expiration-event-bus'
      State: ENABLED
      EventPattern:
        source:
          - aws.ssm
        detail-type:
          - Parameter Store Policy Action   
      Targets:
        - Arn: !Sub "arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${Stage}-${UseCase}-ssm-expiration"
          Id: 'SSMExpiration'  
    DependsOn: ExpirationEventBus 

