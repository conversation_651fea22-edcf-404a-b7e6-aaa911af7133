AWSTemplateFormatVersion: '2010-09-09'
Description: Cloudformation template to create ssm parameters of wvdb
Parameters:
  Stage:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    AllowedValues:
      - dev
      - prod
    Default: dev
  UseCase:
    Description: 'Use Case'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z\-]*$'
    Default: wvdb
  PPNClientSecret:
    Description: 'Client Secret for ppn'
    Type: String
  PPNMasterClientSecret:
    Description: 'Client Secret for ppn master data'
    Type: String
  PPNU2AClientSecret:
    Description: 'Client Secret for ppn u2a'
    Type: String        
Resources:
  ppnClientSecret:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub '${Stage}-${UseCase}-ppn-client-secret'
      DataType: text
      Description: Client Secret for ppn
      Type: String
      Tier: Advanced
      Value: !Sub '${PPNClientSecret}'
      Policies: '[{"Type":"Expiration","Version":"1.0","Attributes":{"Timestamp":"2029-02-01T00:00:00.000Z"}},{"Type":"ExpirationNotification","Version":"1.0","Attributes":{"Before":"30","Unit":"Days"}}]'
  ppnMasterClientSecret:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub '${Stage}-${UseCase}-ppn-master-client-secret'
      DataType: text
      Description: Client Secret for ppn master data
      Type: String
      Tier: Advanced
      Value: !Sub '${PPNMasterClientSecret}'
      Policies: '[{"Type":"Expiration","Version":"1.0","Attributes":{"Timestamp":"2029-02-01T00:00:00.000Z"}},{"Type":"ExpirationNotification","Version":"1.0","Attributes":{"Before":"30","Unit":"Days"}}]'
  ppnU2AClientSecret:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub '${Stage}-${UseCase}-ppn-u2a-client-secret'
      DataType: text
      Description: Client Secret for ppn u2a
      Type: String
      Tier: Advanced
      Value: !Sub '${PPNU2AClientSecret}'
      Policies: '[{"Type":"Expiration","Version":"1.0","Attributes":{"Timestamp":"2029-02-01T00:00:00.000Z"}},{"Type":"ExpirationNotification","Version":"1.0","Attributes":{"Before":"30","Unit":"Days"}}]'            