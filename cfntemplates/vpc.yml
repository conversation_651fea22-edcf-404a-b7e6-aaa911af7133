AWSTemplateFormatVersion: '2010-09-09'
Description: Cloudformation template to VPC of wvdb
Parameters:
  Stage:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    AllowedValues:
      - dev
      - int
      - prod
    Default: dev
  UseCase:
    Description: 'Usecase'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z\-]*$'
    Default: wvdb
  Region:
    Description: 'AWS Region'
    Type: String
    Default: eu-west-1
  pDNSServerCIDR:
    Description: >-
      CIDR range of your DNS server infrastructure, used in the API client security 
      group to restrict egress traffic to only the specific range.
    Type: String
    Default: 0.0.0.0/0
    AllowedPattern: '^([0-9]{1,3}\.){3}[0-9]{1,3}\/[0-9]{1,2}$'
  pEnablePrivateDNS:
    Description: >-
      Enable or disable private DNS resolution on the API Gateway endpoint.  If
      disabled, the client function will connect using the VPC Endpoint DNS name
      rather than the API gateway hostname.
    Type: String
    Default: 'True'
    AllowedValues:
      - 'True'
      - 'False'
Resources:
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: ***********/16
      EnableDnsSupport: true
      EnableDnsHostnames: true
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}'
  PrivateSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Sub '${Region}a'
      CidrBlock: ***********/24
      MapPublicIpOnLaunch: false
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-private-subnet-01'
  PrivateSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Sub '${Region}b'
      CidrBlock: ***********/24
      MapPublicIpOnLaunch: false
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-private-subnet-02'
  PrivateSubnet3:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Sub '${Region}c'
      CidrBlock: ***********/24
      MapPublicIpOnLaunch: false
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-private-subnet-03'
  PrivateRouteTable1:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-private-route-table-01'
  PrivateSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PrivateRouteTable1
      SubnetId: !Ref PrivateSubnet1
  PrivateRouteTable2:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-private-route-table-02'
  PrivateSubnet2RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PrivateRouteTable2
      SubnetId: !Ref PrivateSubnet2
  PrivateRouteTable3:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-private-route-table-03'
  PrivateSubnet3RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PrivateRouteTable3
      SubnetId: !Ref PrivateSubnet3
  PrivateNACL:
    Type: 'AWS::EC2::NetworkAcl'
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-private-subnet-nacl'
  PrivateNACLInboundRule1:
    Type: 'AWS::EC2::NetworkAclEntry'
    Properties:
      NetworkAclId: !Ref PrivateNACL
      RuleNumber: '101'
      Protocol: '6'
      RuleAction: 'allow'
      Egress: 'false'
      CidrBlock: '0.0.0.0/0'
      PortRange:
        From: '1024'
        To: '65535'
  PrivateNACLInboundRule2:
    Type: 'AWS::EC2::NetworkAclEntry'
    Properties:
      NetworkAclId: !Ref PrivateNACL
      RuleNumber: '102'
      Protocol: '6'
      RuleAction: 'allow'
      Egress: 'false'
      CidrBlock: '0.0.0.0/0'
      PortRange:
        From: '443'
        To: '443'
  PrivateNACLOutboundRule1:
    Type: 'AWS::EC2::NetworkAclEntry'
    Properties:
      NetworkAclId: !Ref PrivateNACL
      RuleNumber: '101'
      Protocol: '6'
      RuleAction: 'allow'
      Egress: 'true'
      CidrBlock: '0.0.0.0/0'
      PortRange:
        From: '1024'
        To: '65535'
  PrivateNACLOutboundRule2:
    Type: 'AWS::EC2::NetworkAclEntry'
    Properties:
      NetworkAclId: !Ref PrivateNACL
      RuleNumber: '102'
      Protocol: '6'
      RuleAction: 'allow'
      Egress: 'true'
      CidrBlock: '0.0.0.0/0'
      PortRange:
        From: '443'
        To: '443'

  PrivateSubnetNetworkAclAssociation1:
    Type: 'AWS::EC2::SubnetNetworkAclAssociation'
    Properties:
      SubnetId: !Ref PrivateSubnet1
      NetworkAclId: !Ref PrivateNACL
  PrivateSubnetNetworkAclAssociation2:
    Type: 'AWS::EC2::SubnetNetworkAclAssociation'
    Properties:
      SubnetId: !Ref PrivateSubnet2
      NetworkAclId: !Ref PrivateNACL
  PrivateSubnetNetworkAclAssociation3:
    Type: 'AWS::EC2::SubnetNetworkAclAssociation'
    Properties:
      SubnetId: !Ref PrivateSubnet3
      NetworkAclId: !Ref PrivateNACL

  LambdaSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      VpcId: !Ref VPC
      GroupDescription: Security Group of Lambda Functions in a Private Subnet
      SecurityGroupEgress:
        - Description: Allow HTTPS outbound
          CidrIp: 0.0.0.0/0
          IpProtocol: tcp
          FromPort: '443'
          ToPort: '443'
      Tags:
        - Key: PROJECT
          Value: wvdb
        - Key: STAGE
          Value: !Sub '${Stage}'
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-lambda-sg'

  VPCEClientSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      VpcId: !Ref VPC
      GroupDescription: Allow VPCE clients outbound HTTPS and DNS access
      Tags:
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-vpce-client-sg'
      SecurityGroupEgress:
        - Description: Allow DNS outbound
          CidrIp:
            Ref: pDNSServerCIDR
          IpProtocol: udp
          FromPort: 53
          ToPort: 53

  VPCEClientSecurityGroupEgress:
    Type: AWS::EC2::SecurityGroupEgress
    Properties:
      Description: Allow outbound HTTPS to the API endpoint
      GroupId: !GetAtt VPCEClientSecurityGroup.GroupId
      DestinationSecurityGroupId: !GetAtt VPCEndpointSecurityGroup.GroupId
      IpProtocol: tcp
      FromPort: 443
      ToPort: 443

  VPCEndpointSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      VpcId: !Ref VPC
      GroupDescription: Allow VPCE clients access to the VPC Endpoint
      Tags:
        - Key: Name
          Value: !Sub '${Stage}-${UseCase}-vpce-sg'
      SecurityGroupIngress:
        - Description: Allow inbound HTTPS from the VPCE client security group
          SourceSecurityGroupId: !GetAtt VPCEClientSecurityGroup.GroupId
          IpProtocol: tcp
          FromPort: 443
          ToPort: 443

  KMSVPCEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      SecurityGroupIds:
        - !GetAtt VPCEndpointSecurityGroup.GroupId
      ServiceName: !Sub 'com.amazonaws.${Region}.kms'
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2
        - !Ref PrivateSubnet3
      VpcEndpointType: Interface
      VpcId: !Ref VPC

  S3VPCGatewayEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Action: '*'
            Effect: Allow
            Resource: '*'
            Principal: '*'
      RouteTableIds:
        - !Ref PrivateRouteTable1
        - !Ref PrivateRouteTable2
        - !Ref PrivateRouteTable3
      ServiceName: !Sub 'com.amazonaws.${Region}.s3'
      VpcId: !Ref VPC

  CloudWatchLogsVPCEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      VpcEndpointType: Interface
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Action: '*'
            Effect: Allow
            Resource: '*'
            Principal: '*'
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2
        - !Ref PrivateSubnet3
      ServiceName: !Sub 'com.amazonaws.${Region}.logs'
      VpcId: !Ref VPC

  DynamoDBVPCGatewayEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Action: '*'
            Effect: Allow
            Resource: '*'
            Principal: '*'
      RouteTableIds:
        - !Ref PrivateRouteTable1
        - !Ref PrivateRouteTable2
        - !Ref PrivateRouteTable3
      ServiceName: !Sub 'com.amazonaws.${Region}.dynamodb'
      VpcId: !Ref VPC

  rAPIGatewayEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      VpcEndpointType: Interface
      VpcId:
        Ref: VPC
      ServiceName: !Sub 'com.amazonaws.${AWS::Region}.execute-api'
      PrivateDnsEnabled: !Ref pEnablePrivateDNS
      SecurityGroupIds:
        - !GetAtt VPCEndpointSecurityGroup.GroupId
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2
        - !Ref PrivateSubnet3
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal: '*'
            Action: execute-api:Invoke
            Resource:
              '*'

  VPCFlogLogIamRole:
    Type: 'AWS::IAM::Role'
    Properties:
      RoleName: !Sub '${Stage}-${UseCase}-${AWS::Region}-flowlogs-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: 'vpc-flow-logs.amazonaws.com'
            Action: 'sts:AssumeRole'
      Policies:
        - PolicyName: !Sub '${Stage}-${UseCase}-${AWS::Region}-flowlogs-policy'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'logs:CreateLogStream'
                  - 'logs:PutLogEvents'
                  - 'logs:DescribeLogGroups'
                  - 'logs:DescribeLogStreams'
                  - 'kms:Encrypt'
                  - 'kms:Decrypt'
                  - 'kms:ReEncrypt*'
                  - 'kms:GenerateDataKey*'
                  - 'kms:DescribeKey'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:${Stage}-${UseCase}-flowlogs*'
  VPCFlowLogsLogGroup:
    Type: 'AWS::Logs::LogGroup'
    UpdateReplacePolicy: Retain
    DeletionPolicy: Delete
    Properties:
      LogGroupName: !Sub '${Stage}-${UseCase}-flowlogs'
      KmsKeyId: { 'Fn::ImportValue': !Sub '${Stage}-${UseCase}-cloudwatch-cmk-key-arn' }
      RetentionInDays: 7
  VPCFlowLog:
    Type: 'AWS::EC2::FlowLog'
    Properties:
      DeliverLogsPermissionArn: !GetAtt 'VPCFlogLogIamRole.Arn'
      LogGroupName: !Ref VPCFlowLogsLogGroup
      ResourceId: !Ref VPC
      ResourceType: 'VPC'
      TrafficType: 'ALL'

Outputs:
  PrivateSubnet1Export:
    Description: The subnet ID to use for  private subnet 1 in VPC
    Value: !Ref PrivateSubnet1
    Export:
      Name: !Sub '${Stage}-${UseCase}-vpc-private-subnet-01'
  PrivateSubnet2Export:
    Description: The subnet ID to use for  private subnet 2 in VPC
    Value: !Ref PrivateSubnet2
    Export:
      Name: !Sub '${Stage}-${UseCase}-vpc-private-subnet-02'
  PrivateSubnet3Export:
    Description: The subnet ID to use for  private subnet 3 in VPC
    Value: !Ref PrivateSubnet3
    Export:
      Name: !Sub '${Stage}-${UseCase}-vpc-private-subnet-03'

  LambdaSecurityGroupExport:
    Description: The lambda security group ID
    Value: !GetAtt LambdaSecurityGroup.GroupId
    Export:
      Name: !Sub '${Stage}-${UseCase}-lambda-sg'
  VPCEClientSecurityGroupExport:
    Description: Allow VPCE clients outbound HTTPS and DNS access
    Value: !GetAtt VPCEClientSecurityGroup.GroupId
    Export:
      Name: !Sub '${Stage}-${UseCase}-vpce-client-sg'
  VPCEndpointSecurityGroupExport:
    Description: Allow VPCE clients access to the VPC Endpoint
    Value: !GetAtt VPCEndpointSecurityGroup.GroupId
    Export:
      Name: !Sub '${Stage}-${UseCase}-vpce-sg'

  APIGatewayVPCEndpointID:
    Description: API Gateway VPC Endpoint ID
    Value: !Ref rAPIGatewayEndpoint
    Export:
      Name: !Sub '${Stage}-${UseCase}-apigw-vpce-id'
  S3VPCGatewayEndpointID:
    Description: S3 VPC Endpoint Gateway ID
    Value: !Ref S3VPCGatewayEndpoint
    Export:
      Name: !Sub '${Stage}-${UseCase}-s3gw-vpce-id'
  VPCIDExport:
    Description: The vpc ID
    Value: !Ref VPC
    Export:
      Name: !Sub '${Stage}-${UseCase}-vpc-id'
