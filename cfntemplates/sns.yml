AWSTemplateFormatVersion: '2010-09-09'
Description: Cloudformation template to create sns topics of WVDB
Parameters:
  Stage:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    AllowedValues:
      - dev
      - prod
    Default: dev
  UseCase:
    Description: 'Use Case'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z\-]*$'
    Default: wvdb

Resources:
  ssmExpirationTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub ${Stage}-${UseCase}-ssm-expiration
      DisplayName: !Sub ${Stage}-${UseCase}-ssm-expiration
      KmsMasterKeyId: { 'Fn::ImportValue': !Sub '${Stage}-${UseCase}-sns-cmk-key-arn' }

  varIntTopicPolicy:
    Type: AWS::SNS::TopicPolicy
    Properties:
      Topics:
        - !Ref ssmExpirationTopic
      PolicyDocument:
        Statement:
          - Sid: __default_statement_ID
            Effect: Allow
            Principal:
              AWS: '*'
            Action:
              - SNS:GetTopicAttributes
              - SNS:SetTopicAttributes
              - SNS:AddPermission
              - SNS:RemovePermission
              - SNS:DeleteTopic
              - SNS:Subscribe
              - SNS:ListSubscriptionsByTopic
              - SNS:Publish
            Resource: !Sub arn:aws:sns:${AWS::Region}:${AWS::AccountId}:*
            Condition:
              StringEquals:
                AWS:SourceOwner: !Sub ${AWS::AccountId}
          - Sid: enforceEncryptionOfDataInTransit
            Effect: Allow
            Principal: '*'
            Action:
              - SNS:Publish
            Resource: !Sub arn:aws:sns:${AWS::Region}:${AWS::AccountId}:*
            Condition:
              Bool:
                'aws:SecureTransport': true
          - Sid: denyHttpSubscription
            Effect: Deny
            Principal: '*'
            Action:
              - SNS:Subscribe
            Resource: '*'
            Condition:
              StringEquals:
                'sns:Protocol': 'http'
Outputs:
  ssmExpirationTopicArn:
    Description: 'ssmExpirationTopic ARN.'
    Value: !Ref ssmExpirationTopic
    Export:
      Name: !Sub '${Stage}-${UseCase}-ssm-expiration-arn'
