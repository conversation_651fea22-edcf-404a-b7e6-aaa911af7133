AWSTemplateFormatVersion: '2010-09-09'
Description: Cloudformation template to create kms keys of wvdb
Parameters:
  Stage:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    AllowedValues:
      - dev
      - int
      - prod
    Default: dev
  UseCase:
    Description: 'Usecase'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z\-]*$'
    Default: wvdb
Resources:
  cloudwatchKey:
    Type: AWS::KMS::Key
    Properties:
      EnableKeyRotation: true
      KeySpec: SYMMETRIC_DEFAULT
      KeyUsage: ENCRYPT_DECRYPT
      KeyPolicy:
        # Version: 2012-10-17
        Id: cloudwatchKey
        Statement:
          - Sid: RootKMSPermission
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:root'
            Action:
              - kms:Create*
              - kms:Describe*
              - kms:Enable*
              - kms:List*
              - kms:Put*
              - kms:Update*
              - kms:Revoke*
              - kms:Disable*
              - kms:Get*
              - kms:Delete*
              - kms:ScheduleKeyDeletion
              - kms:CancelKeyDeletion
              - kms:Decrypt
              - kms:GenerateDataKey
            Resource:
              Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: LogsKMSPermission
            Effect: Allow
            Principal:
              Service: !Sub logs.${AWS::Region}.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: LambdaKMSPermission
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: 'Allow access through Amazon services for all principals in the account that are authorized to use services'
            Effect: 'Allow'
            Principal:
              AWS: '*'
            Action:
              - 'kms:CreateGrant'
              - 'kms:Decrypt'
              - 'kms:Describe*'
              - 'kms:Encrypt'
              - 'kms:GenerateDataKey*'
              - 'kms:ReEncrypt*'
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
            Condition:
              StringEquals:
                kms:CallerAccount:
                  - Fn::Sub: ${AWS::AccountId}
              StringLike:
                kms:ViaService:
                  - Fn::Sub: 'cloudwatch.${AWS::Region}.amazonaws.com'
  cloudwatchKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${Stage}-${UseCase}-cloudwatch-cmk'
      TargetKeyId:
        Ref: cloudwatchKey
  s3Key:
    Type: AWS::KMS::Key
    Properties:
      EnableKeyRotation: true
      KeySpec: SYMMETRIC_DEFAULT
      KeyUsage: ENCRYPT_DECRYPT
      KeyPolicy:
        # Version: 2012-10-17
        Id: s3Key
        Statement:
          - Sid: RootKMSPermission
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:root'
            Action:
              - kms:Create*
              - kms:Describe*
              - kms:Enable*
              - kms:List*
              - kms:Put*
              - kms:Update*
              - kms:Revoke*
              - kms:Disable*
              - kms:Get*
              - kms:Delete*
              - kms:ScheduleKeyDeletion
              - kms:CancelKeyDeletion
              - kms:Decrypt
              - kms:GenerateDataKey
            Resource:
              Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: LambdaKMSPermission
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: 'Allow access through Amazon services for all principals in the account that are authorized to use services'
            Effect: 'Allow'
            Principal:
              AWS: '*'
            Action:
              - 'kms:CreateGrant'
              - 'kms:Decrypt'
              - 'kms:Describe*'
              - 'kms:Encrypt'
              - 'kms:GenerateDataKey*'
              - 'kms:ReEncrypt*'
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
            Condition:
              StringEquals:
                kms:CallerAccount:
                  - Fn::Sub: ${AWS::AccountId}
              StringLike:
                kms:ViaService:
                  - Fn::Sub: 's3.${AWS::Region}.amazonaws.com'
  s3KeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${Stage}-${UseCase}-s3-cmk'
      TargetKeyId:
        Ref: s3Key

  dynamodbKey:
    Type: AWS::KMS::Key
    Properties:
      EnableKeyRotation: true
      KeySpec: SYMMETRIC_DEFAULT
      KeyUsage: ENCRYPT_DECRYPT
      Description: Encryption key for dynamodb
      Enabled: true
      KeyPolicy:
        Version: '2012-10-17'
        Statement:
          - Sid: RootKMSPermission
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:root'
            Action:
              - kms:Create*
              - kms:Describe*
              - kms:Enable*
              - kms:List*
              - kms:Put*
              - kms:Update*
              - kms:Revoke*
              - kms:Disable*
              - kms:Get*
              - kms:Delete*
              - kms:ScheduleKeyDeletion
              - kms:CancelKeyDeletion
              - kms:Decrypt
              - kms:GenerateDataKey
            Resource:
              Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: LambdaKMSPermission
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: 'Allow access through Amazon services for all principals in the account that are authorized to use services'
            Effect: 'Allow'
            Principal:
              AWS: '*'
            Action:
              - 'kms:CreateGrant'
              - 'kms:Decrypt'
              - 'kms:Describe*'
              - 'kms:Encrypt'
              - 'kms:GenerateDataKey*'
              - 'kms:ReEncrypt*'
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
            Condition:
              StringEquals:
                kms:CallerAccount:
                  - Fn::Sub: ${AWS::AccountId}
              StringLike:
                kms:ViaService:
                  - Fn::Sub: 'dynamodb.${AWS::Region}.amazonaws.com'
  dynamodbKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${Stage}-${UseCase}-dynamodb-cmk'
      TargetKeyId:
        Ref: dynamodbKey

  lambdaEnvKey:
    Type: AWS::KMS::Key
    Properties:
      EnableKeyRotation: true
      KeySpec: SYMMETRIC_DEFAULT
      KeyUsage: ENCRYPT_DECRYPT
      KeyPolicy:
        # Version: 2012-10-17
        Id: lambdaEnvKey
        Statement:
          - Sid: RootKMSPermission
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:root'
            Action:
              - kms:Create*
              - kms:Describe*
              - kms:Enable*
              - kms:List*
              - kms:Put*
              - kms:Update*
              - kms:Revoke*
              - kms:Disable*
              - kms:Get*
              - kms:Delete*
              - kms:ScheduleKeyDeletion
              - kms:CancelKeyDeletion
              - kms:Decrypt
              - kms:Encrypt
              - kms:GenerateDataKey
            Resource:
              Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: LambdaKMSPermission
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: CloudWatchKMSPermission
            Effect: Allow
            Principal:
              Service: cloudwatch.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: 'Allow access through Amazon services for all principals in the account that are authorized to use services'
            Effect: 'Allow'
            Principal:
              AWS: '*'
            Action:
              - 'kms:CreateGrant'
              - 'kms:Decrypt'
              - 'kms:Describe*'
              - 'kms:Encrypt'
              - 'kms:GenerateDataKey*'
              - 'kms:ReEncrypt*'
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
            Condition:
              StringEquals:
                kms:CallerAccount:
                  - Fn::Sub: ${AWS::AccountId}
              StringLike:
                kms:ViaService:
                  - Fn::Sub: 'lambda.${AWS::Region}.amazonaws.com'
  lambdaEnvKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${Stage}-${UseCase}-lambda-env-cmk'
      TargetKeyId:
        Ref: lambdaEnvKey

  snsKey:
    Type: AWS::KMS::Key
    Properties:
      EnableKeyRotation: true
      KeySpec: SYMMETRIC_DEFAULT
      KeyUsage: ENCRYPT_DECRYPT
      KeyPolicy:
        # Version: 2012-10-17
        Id: snsKey
        Statement:
          - Sid: RootKMSPermission
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:root'
            Action:
              - kms:Create*
              - kms:Describe*
              - kms:Enable*
              - kms:List*
              - kms:Put*
              - kms:Update*
              - kms:Revoke*
              - kms:Disable*
              - kms:Get*
              - kms:Delete*
              - kms:ScheduleKeyDeletion
              - kms:CancelKeyDeletion
              - kms:Decrypt
              - kms:GenerateDataKey
            Resource:
              Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: LambdaKMSPermission
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: CloudWatchKMSPermission
            Effect: Allow
            Principal:
              Service: cloudwatch.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: 'Allow access through Amazon services for all principals in the account that are authorized to use services'
            Effect: 'Allow'
            Principal:
              AWS: '*'
            Action:
              - 'kms:CreateGrant'
              - 'kms:Decrypt'
              - 'kms:Describe*'
              - 'kms:Encrypt'
              - 'kms:GenerateDataKey*'
              - 'kms:ReEncrypt*'
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
            Condition:
              StringEquals:
                kms:CallerAccount:
                  - Fn::Sub: ${AWS::AccountId}
              StringLike:
                kms:ViaService:
                  - Fn::Sub: 'sns.${AWS::Region}.amazonaws.com'
  snsKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${Stage}-${UseCase}-sns-cmk'
      TargetKeyId:
        Ref: snsKey
    

  ssmKey:
    Type: AWS::KMS::Key
    Properties:
      EnableKeyRotation: true
      KeySpec: SYMMETRIC_DEFAULT
      KeyUsage: ENCRYPT_DECRYPT
      Description: Encryption key for ssm
      Enabled: true
      KeyPolicy:
        Version: '2012-10-17'
        Statement:
          - Sid: RootKMSPermission
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:root'
            Action:
              - kms:Create*
              - kms:Describe*
              - kms:Enable*
              - kms:List*
              - kms:Put*
              - kms:Update*
              - kms:Revoke*
              - kms:Disable*
              - kms:Get*
              - kms:Delete*
              - kms:ScheduleKeyDeletion
              - kms:CancelKeyDeletion
              - kms:Decrypt
              - kms:GenerateDataKey
            Resource:
              Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: LambdaKMSPermission
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action:
              - kms:Decrypt
              - kms:GenerateDataKey
              - kms:Encrypt
              - kms:DescribeKey
              - kms:ReEncrypt
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
          - Sid: 'Allow access through Amazon services for all principals in the account that are authorized to use services'
            Effect: 'Allow'
            Principal:
              AWS: '*'
            Action:
              - 'kms:CreateGrant'
              - 'kms:Decrypt'
              - 'kms:Describe*'
              - 'kms:Encrypt'
              - 'kms:GenerateDataKey*'
              - 'kms:ReEncrypt*'
            Resource:
              - Fn::Sub: arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*
            Condition:
              StringEquals:
                kms:CallerAccount:
                  - Fn::Sub: ${AWS::AccountId}
              StringLike:
                kms:ViaService:
                  - Fn::Sub: 'ssm.${AWS::Region}.amazonaws.com'
  ssmKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${Stage}-${UseCase}-ssm-cmk'
      TargetKeyId:
        Ref: ssmKey


Outputs:
  s3KeyAlias:
    Description: 'KMS Key Alias of S3 Buckets'
    Value:
      Ref: 's3KeyAlias'
    Export:
      Name: !Sub '${Stage}-${UseCase}-s3-cmk-alias'
  s3KeyId:
    Description: 's3Key id.'
    Value: !Ref s3Key
    Export:
      Name: !Sub '${Stage}-${UseCase}-s3-cmk-key-id'
  s3KeyArn:
    Description: 's3Key ARN.'
    Value: !GetAtt 's3Key.Arn'
    Export:
      Name: !Sub '${Stage}-${UseCase}-s3-cmk-key-arn'

  cloudwatchKeyAlias:
    Description: 'KMS Key Alias of CloudWatch Log Groups'
    Value:
      Ref: 'cloudwatchKeyAlias'
    Export:
      Name: !Sub '${Stage}-${UseCase}-cloudwatch-cmk-alias'
  cloudwatchKeyId:
    Description: 'cloudwatchKey id.'
    Value: !Ref cloudwatchKey
    Export:
      Name: !Sub '${Stage}-${UseCase}-cloudwatch-cmk-key-id'
  cloudwatchKeyArn:
    Description: 'cloudwatchKey ARN.'
    Value: !GetAtt 'cloudwatchKey.Arn'
    Export:
      Name: !Sub '${Stage}-${UseCase}-cloudwatch-cmk-key-arn'

  dynamodbKeyAlias:
    Description: 'KMS Key Alias of DynamoDB'
    Value:
      Ref: 'dynamodbKeyAlias'
    Export:
      Name: !Sub '${Stage}-${UseCase}-dynamodb-cmk-alias'
  dynamodbKeyId:
    Description: 'dynamodbKey id.'
    Value: !Ref dynamodbKey
    Export:
      Name: !Sub '${Stage}-${UseCase}-dynamodb-cmk-key-id'
  dynamodbKeyArn:
    Description: 'dynamodbKey ARN.'
    Value: !GetAtt 'dynamodbKey.Arn'
    Export:
      Name: !Sub '${Stage}-${UseCase}-dynamodb-cmk-key-arn'

  lambdaEnvKeyAlias:
    Description: 'KMS Key Alias of Lambda Env-Variablen'
    Value:
      Ref: 'lambdaEnvKeyAlias'
    Export:
      Name: !Sub '${Stage}-${UseCase}-lambda-env-cmk-alias'
  lambdaEnvKeyId:
    Description: 'lambdaEnvKey id.'
    Value: !Ref lambdaEnvKey
    Export:
      Name: !Sub '${Stage}-${UseCase}-lambda-env-cmk-key-id'
  lambdaEnvKeyArn:
    Description: 'lambdaEnvKey ARN.'
    Value: !GetAtt 'lambdaEnvKey.Arn'
    Export:
      Name: !Sub '${Stage}-${UseCase}-lambda-env-cmk-key-arn'
      
  snsKeyAlias:
    Description: "KMS Key Alias of SNS Topics"
    Value:
      Ref: "snsKeyAlias"
    Export:
      Name: !Sub '${Stage}-${UseCase}-sns-cmk-key-alias'
  snsKeyId:
    Description: "snsKey id."
    Value: !Ref snsKey
    Export:
      Name: !Sub '${Stage}-${UseCase}-sns-cmk-key-id'
  snsKeyArn:
    Description: "snsKey ARN."
    Value: !GetAtt "snsKey.Arn"
    Export:
      Name: !Sub '${Stage}-${UseCase}-sns-cmk-key-arn'
      
  ssmKeyAlias:
    Description: 'KMS Key Alias of SSM'
    Value:
      Ref: 'ssmKeyAlias'
    Export:
      Name: !Sub '${Stage}-${UseCase}-ssm-cmk-alias'
  ssmKeyId:
    Description: 'ssmKey id.'
    Value: !Ref ssmKey
    Export:
      Name: !Sub '${Stage}-${UseCase}-ssm-cmk-key-id'
  ssmKeyArn:
    Description: 'ssmKey ARN.'
    Value: !GetAtt 'ssmKey.Arn'
    Export:
      Name: !Sub '${Stage}-${UseCase}-ssm-cmk-key-arn'
