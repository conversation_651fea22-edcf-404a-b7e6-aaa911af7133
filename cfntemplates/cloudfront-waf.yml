AWSTemplateFormatVersion: '2010-09-09'
Description: Cloudformation template to cloudfront WAF of wvdb
Parameters:
  Stage:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    AllowedValues:
      - dev
      - prod
    Default: dev
  UseCase:
    Description: 'Use Case'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z\-]*$'
    Default: wvdb

Resources:
  CloudFrontWebACL:
    Type: AWS::WAFv2::WebACL
    Properties:
      Name: !Sub '${Stage}-${UseCase}-CloudFront-WebACL'
      Scope: CLOUDFRONT
      DefaultAction:
        Allow: {}
      Description: 'Web ACL of WVDB'
      Rules:
        - Name: !Sub '${Stage}-${UseCase}-rate-limit'
          Priority: 1
          Action:
            Block: {}
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: !Sub '${Stage}-${UseCase}-rate-limit'
          Statement:
            RateBasedStatement:
              AggregateKeyType: 'IP'
              #if the threshold for the rate-based rule is set to 2,000, the rule will block all IPs that are making more than 2,000 requests in a rolling 5-minute period.
              Limit: 2000
        - Name: !Sub '${Stage}-${UseCase}-AWS-AWSManagedRulesCommonRuleSet'
          Priority: 2
          OverrideAction:
            None: {}
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: !Sub '${Stage}-${UseCase}-MetricForAMRCRS'
          Statement:
            ManagedRuleGroupStatement:
              VendorName: 'AWS'
              Name: 'AWSManagedRulesCommonRuleSet'
        - Name: !Sub '${Stage}-${UseCase}-AWS-AWSManagedRulesBotControlRuleSet'
          Priority: 3
          OverrideAction:
            None: {}
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: !Sub '${Stage}-${UseCase}-MetricForAMRBCRS'
          Statement:
            ManagedRuleGroupStatement:
              VendorName: 'AWS'
              Name: 'AWSManagedRulesBotControlRuleSet'
        - Name: !Sub '${Stage}-${UseCase}-AWS-AWSManagedRulesAmazonIpReputationList'
          Priority: 4
          OverrideAction:
            None: {}
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: !Sub '${Stage}-${UseCase}-MetricForAMRAIPRL'
          Statement:
            ManagedRuleGroupStatement:
              VendorName: 'AWS'
              Name: 'AWSManagedRulesAmazonIpReputationList'
        - Name: !Sub '${Stage}-${UseCase}-AWS-AWSManagedRulesKnownBadInputsRuleSet'
          Priority: 5
          OverrideAction:
            None: {}
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: !Sub '${Stage}-${UseCase}-MetricForAMRKBIRS'
          Statement:
            ManagedRuleGroupStatement:
              VendorName: 'AWS'
              Name: 'AWSManagedRulesKnownBadInputsRuleSet'
      VisibilityConfig:
        SampledRequestsEnabled: true
        CloudWatchMetricsEnabled: true
        MetricName: !Sub '${Stage}-${UseCase}-WebACLMetric'

Outputs:
  CloudFrontWebACLArn:
    Description: 'CloudFrontWebACL ARN.'
    Value: !GetAtt CloudFrontWebACL.Arn
    Export:
      Name: !Sub '${Stage}-${UseCase}-cloudfront-web-acl-arn'
  CloudFrontWebACLId:
    Description: 'CloudFrontWebACL Id.'
    Value: !GetAtt CloudFrontWebACL.Id
    Export:
      Name: !Sub '${Stage}-${UseCase}-cloudfront-web-acl-id'
