module.exports = {
  extends: ['@commitlint/config-conventional'],
  plugins: ['commitlint-plugin-function-rules'],
  rules: {
    'type-case': [2, 'always', ['lower-case', 'upper-case']], // <PERSON><PERSON> sicher, dass dies deinen Anforderungen entspricht
    'type-enum': [
      2,
      'always',
      ['build', 'chore', 'ci', 'docs', 'feat', 'fix', 'perf', 'refactor', 'revert', 'style', 'test', 'WIP'], // Füge 'WIP' hier hinzu
    ],
    'subject-case': [2, 'always', ['sentence-case', 'start-case', 'pascal-case', 'upper-case', 'lower-case']],
    'function-rules/type-enum': [
      2,
      'always',
      (parsed) => {
        const ticketNumberPattern = /^(WVDB|PARA|WERK|RPS1|PROD)-\d+/;
        if ((parsed.type === 'feat' || parsed.type === 'fix') && !parsed.subject.match(ticketNumberPattern)) {
          return [false, 'feat und fix Commits müssen eine Ticketnummer im Format PARA-<Nummer>|WVDB-<Nummer>|WERK-<Nummer>|RPS1-<Nummer>|PROD-<Nummer> enthalten'];
        }
        return [true];
      },
    ],
  },
};
