# 🎯 SUPER EINFACHE TOKEN-REFRESH LÖSUNG

## Problem
Sessions laufen nach 15 Minuten ab und Backend-Calls schlagen fehl.

## Lösung (Ultra-simpel!)

### 1. **Authorizer bleibt fast unverändert**
- Validiert Token wie bisher
- Bei ungültigem Token: `throw new Error('Unauthorized')`
- **Keine komplizierte Token-Refresh-Logik im Authorizer!**

### 2. **GatewayResponse redirected intelligent**
```typescript
// Statt zum Login, redirect zum Refresh-Endpoint
refreshUrl = `https://${domainName}/refresh-token?redirect=$request.path$request.querystring`;
```

### 3. **Refresh-Endpoint macht alles**
- **GET /refresh-token** (kein Authorizer nötig)
- Versucht Token-Refresh mit vorhandenem RefreshToken-Cookie
- **Bei Erfolg:** Setzt neue Cookies + redirect zur ursprünglichen URL
- **Be<PERSON>:** Redirect zum Login

## Implementierte Dateien

### ✅ `refreshTokenLambda.ts` (NEU)
- Extrahiert RefreshToken aus Cookie
- Ruft PPN Token-Endpoint auf
- Setzt neue Cookies bei Erfolg
- Redirected zurück zur ursprünglichen URL
- Fallback zum Login bei Fehlern

### ✅ `GatewayStack.ts` (Erweitert)
- Neuer `/refresh-token` Endpoint ohne Authorizer
- GatewayResponse redirected zu Refresh statt Login
- Environment-Variablen für Refresh-Lambda

### ✅ `authorizerLambda.ts` (Minimal geändert)
- Nur Kommentar geändert: "redirecting to refresh endpoint"
- Keine komplizierte Logik hinzugefügt

### ✅ `codeExchangeLambda.ts` (Bereits erweitert)
- RefreshToken wird als Cookie gesetzt
- Beide Tokens haben gleiche Expiration

## Ablauf für User

### Szenario 1: Token noch gültig
1. User macht API-Call
2. Authorizer validiert Token ✅
3. Request geht durch

### Szenario 2: Token abgelaufen
1. User macht API-Call
2. Authorizer: Token ungültig → `Unauthorized`
3. GatewayResponse: Redirect zu `/refresh-token?redirect=/original/path`
4. Refresh-Lambda: Token-Refresh erfolgreich
5. Neue Cookies gesetzt + Redirect zu `/original/path`
6. User merkt nichts! 🎉

### Szenario 3: Refresh fehlschlägt
1. User macht API-Call
2. Authorizer: Token ungültig → `Unauthorized`
3. GatewayResponse: Redirect zu `/refresh-token?redirect=/original/path`
4. Refresh-Lambda: Refresh fehlgeschlagen
5. Redirect zum Login
6. Nach Login: Redirect zu `/original/path`

## Warum diese Lösung perfekt ist

### ✅ **Ultra-simpel**
- Authorizer bleibt praktisch unverändert
- Nutzt bestehende Redirect-Mechanismen
- Nur 1 neuer Endpoint

### ✅ **Transparent für User**
- Bei gültigen Refresh-Tokens: Keine Unterbrechung
- User bleibt auf der gewünschten Seite
- Nur bei echten Problemen: Login erforderlich

### ✅ **Robust**
- Fallback zum Login bei allen Fehlern
- Keine komplizierte Error-Handling
- Funktioniert mit bestehender Architektur

### ✅ **Sicher**
- RefreshToken als HttpOnly Cookie
- Client-Secret aus AWS Parameter Store
- Keine Token-Logik im Frontend nötig

## Das war's! 🚀

**Keine komplizierte Session-Manager-JavaScript**
**Keine Token-Refresh-Logik im Authorizer**
**Keine neuen Frontend-Dependencies**

Einfach deployen und es funktioniert! User können stundenlang auf der Seite bleiben ohne Unterbrechung.
